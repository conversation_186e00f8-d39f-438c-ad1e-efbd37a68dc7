{"__meta": {"id": "01K40C99DGFX00H50A84HK0T7S", "datetime": "2025-08-31 16:35:11", "utime": **********.536921, "method": "GET", "uri": "/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.335013, "end": **********.547477, "duration": 0.2124640941619873, "duration_str": "212ms", "measures": [{"label": "Booting", "start": **********.335013, "relative_start": 0, "end": **********.517495, "relative_end": **********.517495, "duration": 0.*****************, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.517506, "relative_start": 0.*****************, "end": **********.547479, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "29.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.52728, "relative_start": 0.*****************, "end": **********.530945, "relative_end": **********.530945, "duration": 0.0036649703979492188, "duration_str": "3.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.535512, "relative_start": 0.****************, "end": **********.535599, "relative_end": **********.535599, "duration": 8.702278137207031e-05, "duration_str": "87μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.535611, "relative_start": 0.*****************, "end": **********.535622, "relative_end": **********.535622, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "213ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-785314465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-785314465\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-930377226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-930377226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-466268531 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"749 characters\">cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6ImQ3R2tzUzhhZ0RVTDMvdVU3UjFqb0E9PSIsInZhbHVlIjoiU0Q5QVp5dldXSTUzaXlnUDVIK0dOOVhzem5ubnRETEdXL2FsSmxmRzdEUDRHQWFYSmJoNEN5QS82WGRMcm1QUlhpTEhXYTdGNmlmTDBXM28zQVVtZ3BDMEc2RGV4K08yTFZCdVNibmF2a1h0dWxDeU1JVVFrRDFyOCttV1FQOW0iLCJtYWMiOiI3NmExYzgxMjM0MjZmMzA1NDQyOGIyY2ZhY2VjYmEzMDA3NWY3MTBmMmUwMDhmMTNkZDA2NzljZjk0MWVjMGJlIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InNBb1FudGJ5eTZwTkg5QmkzUTBsSVE9PSIsInZhbHVlIjoic3RzR3VUMyt4MGNQc1pTRFBRRXZPU0ZMbWpJMmh4QkNYendxV2J5VHdRWkFGQVpUa3hVSURUSFhJSFRFcUlNLzR4LzBvWENiSFJlTlhGdjZ2K1V3WXFQTzB3dCtESDZLYWRQQ0ZuQzV2SmMvNm90eThXK0JUUlg4Sjgwd0k5Q3giLCJtYWMiOiI1Yjg2NTczNTQ3Yjc5MTRjNTUwOWY5YjBkNjk2ODQzY2Y2ZTc2OWUxMmFhNzkzOWY0YzQ5ZmEyNmNlYWRmNDY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466268531\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1437333004 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImQ3R2tzUzhhZ0RVTDMvdVU3UjFqb0E9PSIsInZhbHVlIjoiU0Q5QVp5dldXSTUzaXlnUDVIK0dOOVhzem5ubnRETEdXL2FsSmxmRzdEUDRHQWFYSmJoNEN5QS82WGRMcm1QUlhpTEhXYTdGNmlmTDBXM28zQVVtZ3BDMEc2RGV4K08yTFZCdVNibmF2a1h0dWxDeU1JVVFrRDFyOCttV1FQOW0iLCJtYWMiOiI3NmExYzgxMjM0MjZmMzA1NDQyOGIyY2ZhY2VjYmEzMDA3NWY3MTBmMmUwMDhmMTNkZDA2NzljZjk0MWVjMGJlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InNBb1FudGJ5eTZwTkg5QmkzUTBsSVE9PSIsInZhbHVlIjoic3RzR3VUMyt4MGNQc1pTRFBRRXZPU0ZMbWpJMmh4QkNYendxV2J5VHdRWkFGQVpUa3hVSURUSFhJSFRFcUlNLzR4LzBvWENiSFJlTlhGdjZ2K1V3WXFQTzB3dCtESDZLYWRQQ0ZuQzV2SmMvNm90eThXK0JUUlg4Sjgwd0k5Q3giLCJtYWMiOiI1Yjg2NTczNTQ3Yjc5MTRjNTUwOWY5YjBkNjk2ODQzY2Y2ZTc2OWUxMmFhNzkzOWY0YzQ5ZmEyNmNlYWRmNDY5IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437333004\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1841171940 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1986</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">1f3427cbbd4ecbda6a1f2680b426de92</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 15:35:11 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841171940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-535143017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-535143017\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}