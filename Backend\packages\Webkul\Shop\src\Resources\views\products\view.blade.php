@inject ('reviewHelper', 'Webkul\Product\Helpers\Review')
@inject ('productViewHelper', 'Webkul\Product\Helpers\View')

@php
    $avgRatings = $reviewHelper->getAverageRating($product);

    $percentageRatings = $reviewHelper->getPercentageRating($product);

    $customAttributeValues = $productViewHelper->getAdditionalData($product);
   
    $attributeData = collect($customAttributeValues)->filter(fn ($item) => ! empty($item['value']));
   
@endphp

<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="{{ trim($product->meta_description) != "" ? $product->meta_description : \Illuminate\Support\Str::limit(strip_tags($product->description), 120, '') }}"/>

    <meta name="keywords" content="{{ $product->meta_keywords }}"/>

    @if (core()->getConfigData('catalog.rich_snippets.products.enable'))
        <script type="application/ld+json">
            {!! app('Webkul\Product\Helpers\SEO')->getProductJsonLd($product) !!}
        </script>
    @endif

    <?php $productBaseImage = product_image()->getProductBaseImage($product); ?>

    <meta name="twitter:card" content="summary_large_image" />

    <meta name="twitter:title" content="{{ $product->name }}" />

    <meta name="twitter:description" content="{!! htmlspecialchars(trim(strip_tags($product->description))) !!}" />

    <meta name="twitter:image:alt" content="" />

    <meta name="twitter:image" content="{{ $productBaseImage['medium_image_url'] }}" />

    <meta property="og:type" content="og:product" />

    <meta property="og:title" content="{{ $product->name }}" />

    <meta property="og:image" content="{{ $productBaseImage['medium_image_url'] }}" />

    <meta property="og:description" content="{!! htmlspecialchars(trim(strip_tags($product->description))) !!}" />

    <meta property="og:url" content="{{ route('shop.product_or_category.index', $product->url_key) }}" />
@endPush

<!-- Page Layout -->
<x-shop::layouts>
    <!-- Page Title -->
    <x-slot:title>
        {{ trim($product->meta_title) != "" ? $product->meta_title : $product->name }}
    </x-slot>

    {!! view_render_event('bagisto.shop.products.view.before', ['product' => $product]) !!}

    <!-- Breadcrumbs -->
    @if ((core()->getConfigData('general.general.breadcrumbs.shop')))
        <div class="flex justify-center px-7 max-lg:hidden">
            <x-shop::breadcrumbs
                name="product"
                :entity="$product"
            />
        </div>
    @endif

    <!-- Product Information Vue Component -->
    <v-product>
        <x-shop::shimmer.products.view />
    </v-product>

    <!-- Information Section -->
    <div class="container mt-16 px-[60px] max-1180:px-5 max-sm:px-4">
        <!-- Two Column Layout: 6:4 ratio -->
        <div class="grid grid-cols-10 gap-8 max-lg:grid-cols-1 max-lg:gap-6">
            <!-- Left Column: Information Accordions (60% width) -->
            <div class="col-span-6 max-lg:col-span-1">
                <!-- Description Accordion -->
                <x-shop::accordion
                    class="border border-gray-200 @if(count($attributeData)) rounded-t-lg @else rounded-lg @endif overflow-hidden shadow-sm accordion-custom accordion-top"
                    :is-active="true"
                >
                    <x-slot:header class="bg-white hover:bg-gray-50 transition-all duration-200 !py-4 !px-6 border-b border-gray-200">
                        <div class="flex items-center justify-between w-full">
                            <p class="text-lg font-semibold text-black">
                                @lang('shop::app.products.view.description')
                            </p>
                            <!-- 使用内置的 icon-arrow-up/down 类 -->
                        </div>
                    </x-slot:header>

                    <x-slot:content class="!px-6 !py-4 bg-white">
                        {!! view_render_event('bagisto.shop.products.view.description.before', ['product' => $product]) !!}
                        
                        <div class="text-lg text-zinc-500 leading-relaxed">
                            {!! $product->description !!}
                        </div>
                        
                        {!! view_render_event('bagisto.shop.products.view.description.after', ['product' => $product]) !!}
                    </x-slot:content>
                </x-shop::accordion>

                <!-- Separator Line -->
                @if(count($attributeData))
                    <div class="border-b border-gray-200"></div>
                @endif

                <!-- Additional Information Accordion -->
                @if(count($attributeData))
                    <x-shop::accordion
                        class="border-l border-r border-b border-gray-200 rounded-b-lg overflow-hidden shadow-sm accordion-custom accordion-bottom"
                        :is-active="false"
                    >
                        <x-slot:header class="bg-white hover:bg-gray-50 transition-all duration-200 !py-4 !px-6 border-b border-gray-200">
                            <div class="flex items-center justify-between w-full">
                                <p class="text-lg font-semibold text-black">
                                    @lang('shop::app.products.view.additional-information')
                                </p>
                                <!-- 使用内置的 icon-arrow-up/down 类 -->
                            </div>
                        </x-slot:header>

                        <x-slot:content class="!px-6 !py-4 bg-white">
                            <div class="grid max-w-full grid-cols-[auto_1fr] gap-4 text-base">
                                @foreach ($customAttributeValues as $customAttributeValue)
                                    @if (! empty($customAttributeValue['value']))
                                        <div class="grid">
                                            <p class="text-base font-medium text-black">
                                                {!! $customAttributeValue['label'] !!}
                                            </p>
                                        </div>

                                        @if ($customAttributeValue['type'] == 'file')
                                            <a
                                                href="{{ Storage::url($product[$customAttributeValue['code']]) }}"
                                                download="{{ $customAttributeValue['label'] }}"
                                                class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                                            >
                                                <span class="icon-download text-2xl"></span>
                                            </a>
                                        @elseif ($customAttributeValue['type'] == 'image')
                                            <a
                                                href="{{ Storage::url($product[$customAttributeValue['code']]) }}"
                                                download="{{ $customAttributeValue['label'] }}"
                                                class="inline-block"
                                            >
                                                <img
                                                    class="h-8 w-8 rounded object-cover shadow-sm hover:shadow-md transition-shadow"
                                                    src="{{ Storage::url($customAttributeValue['value']) }}"
                                                    alt="{{ $customAttributeValue['label'] }}"
                                                />
                                            </a>
                                        @elseif ($customAttributeValue['type'] == 'multiimage')
                                            <p class="text-base text-zinc-500">Multi Image</p> 
                                        @else
                                            <div class="grid">
                                                <p class="text-base text-zinc-500">
                                                    {!! $customAttributeValue['value'] !!}
                                                </p>
                                            </div>
                                        @endif
                                    @endif
                                @endforeach
                            </div>
                        </x-slot:content>
                    </x-shop::accordion>
                @endif
            </div>

            <!-- Right Column: Product Information Cards (40% width) -->
            <div class="col-span-4 max-lg:col-span-1 space-y-6 product-info-sidebar">
                <!-- Checkout Securely Card -->
                <div class="info-card bg-white rounded-lg border border-gray-200 shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            @lang('shop::app.products.view.checkout-securely-with')
                        </h3>
                        
                        <!-- Payment Methods Grid -->
                        <div class="grid grid-cols-4 gap-2 max-md:grid-cols-2 max-sm:grid-cols-1">
                            <!-- PayPal -->
                            <div class="flex items-center justify-center">
                                <img src="{{ bagisto_asset('images/paypal2.png', 'shop') }}" 
                                     alt="PayPal" 
                                     class="max-w-full h-auto" />
                            </div>
                            
                            <!-- Visa -->
                            <div class="flex items-center justify-center">
                                <img src="{{ bagisto_asset('images/visa.png', 'shop') }}" 
                                     alt="Visa" 
                                     class="max-w-full h-auto" />
                            </div>
                            
                            <!-- Mastercard -->
                            <div class="flex items-center justify-center">
                                <img src="{{ bagisto_asset('images/wxd.png', 'shop') }}" 
                                     alt="Mastercard" 
                                     class="max-w-full h-auto" />
                            </div>
                            
                            <!-- American Express -->
                            <div class="flex items-center justify-center">
                                <img src="{{ bagisto_asset('images/amex.png', 'shop') }}" 
                                     alt="American Express" 
                                     class="max-w-full h-auto" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Details Card -->
                <div class="info-card bg-white rounded-lg border border-gray-200 shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
                    <h3 class="text-lg font-semibold text-black mb-4 border-b border-gray-100 pb-2">
                        @lang('shop::app.products.view.product-details')
                    </h3>
                    
                    <div class="space-y-3">
                        <!-- Product Number -->
                        @php
                            $productNumberAttribute = $product->attribute_values->where('attribute.code', 'product_number')->first();
                            $productNumber = '';
                            if ($productNumberAttribute && $productNumberAttribute->text_value) {
                                $productNumber = $productNumberAttribute->text_value;
                            } elseif ($productNumberAttribute && $productNumberAttribute->integer_value) {
                                $productNumber = $productNumberAttribute->integer_value;
                            }
                        @endphp
                        @if($productNumber)
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-zinc-600 font-medium">@lang('shop::app.products.view.product-number')</span>
                                <span class="text-sm text-black font-mono">{{ $productNumber }}</span>
                            </div>
                        @endif

                        <!-- Brand -->
                        @php
                            $brandAttribute = $product->attribute_values->where('attribute.code', 'brand')->first();
                            $brandLabel = '';
                            if ($brandAttribute && $brandAttribute->text_value) {
                                $brandLabel = $brandAttribute->text_value;
                            } elseif ($brandAttribute && $brandAttribute->integer_value) {
                                $brandOption = app('Webkul\Attribute\Repositories\AttributeOptionRepository')
                                    ->find($brandAttribute->integer_value);
                                $brandLabel = $brandOption ? $brandOption->admin_name : '';
                            }
                        @endphp
                        @if($brandLabel)
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-zinc-600 font-medium">@lang('shop::app.products.view.brand')</span>
                                <span class="text-sm text-black">{{ $brandLabel }}</span>
                            </div>
                        @endif

                        <!-- Weight -->
                        @if($product->weight)
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-zinc-600 font-medium">@lang('shop::app.products.view.weight')</span>
                                <span class="text-sm text-black">{{ $product->weight }} {{ core()->getConfigData('catalog.products.storefront.weight_unit') ?? 'kg' }}</span>
                            </div>
                        @endif

                        <!-- Status -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-zinc-600 font-medium">@lang('shop::app.products.view.availability')</span>
                            <span class="text-sm {{ $product->haveSufficientQuantity(1) ? 'text-green-600' : 'text-red-600' }} font-medium">
                                {{ $product->haveSufficientQuantity(1) ? trans('shop::app.products.view.in-stock') : trans('shop::app.products.view.out-of-stock') }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Shipping Information Card -->
                <div class="info-card bg-white rounded-lg border border-gray-200 shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
                    <h3 class="text-lg font-semibold text-black mb-4 border-b border-gray-100 pb-2">
                        @lang('shop::app.products.view.shipping-info')
                    </h3>
                    
                    <div class="space-y-3">
                        <!-- Free Shipping -->
                        <div class="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                            <div class="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-blue-50">
                                <span class="icon-Free-Shipping text-blue-600 text-base feature-icon"></span>
                            </div>
                            <div class="grid">
                                <p class="text-sm font-medium text-black">@lang('shop::app.products.view.free-shipping')</p>
                                <p class="text-xs text-zinc-500 mt-1">@lang('shop::app.products.view.free-shipping-desc')</p>
                            </div>
                        </div>

                        <!-- Return Policy -->
                        <div class="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                            <div class="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-green-50">
                                <span class="icon-compare-1 text-green-600 text-base feature-icon"></span>
                            </div>
                            <div class="grid">
                                <p class="text-sm font-medium text-black">@lang('shop::app.products.view.return-policy')</p>
                                <p class="text-xs text-zinc-500 mt-1">@lang('shop::app.products.view.return-policy-desc')</p>
                            </div>
                        </div>

                        <!-- Secure Payment -->
                        <div class="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                            <div class="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-purple-50">
                                <span class="icon-gdpr-safe text-purple-600 text-base feature-icon"></span>
                            </div>
                            <div class="grid">
                                <p class="text-sm font-medium text-black">@lang('shop::app.products.view.secure-payment')</p>
                                <p class="text-xs text-zinc-500 mt-1">@lang('shop::app.products.view.secure-payment-desc')</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Multi Image Gallery Section -->
    @php
        $multiImageAttributes = collect($customAttributeValues)->filter(function ($attribute) {
            return $attribute['type'] === 'multiimage' && !empty($attribute['value']) && is_array($attribute['value']);
        });
    @endphp

    @if($multiImageAttributes->isNotEmpty())
        <div class="container mt-16 px-[60px] max-1180:px-5 max-sm:px-4">
            @foreach($multiImageAttributes as $attribute)
                <div class="mb-12">
                    <!-- Images List - One per row -->
                    <div class="grid grid-cols-1 gap-6">
                        @foreach($attribute['value'] as $index => $imagePath)
                            <div class="overflow-hidden rounded-lg bg-gray-100 shadow-sm transition-all duration-300 hover:shadow-lg">
                                <img 
                                    src="{{ asset('storage/' . $imagePath) }}" 
                                    alt="{{ $attribute['label'] }} {{ $index + 1 }}"
                                    class="w-full h-auto object-cover transition-transform duration-300 hover:scale-[1.02]"
                                    loading="lazy"
                                    onerror="console.log('Image failed to load:', '{{ asset('storage/' . $imagePath) }}');"
                                />
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Featured Products -->
    <x-shop::products.carousel
        :title="trans('shop::app.products.view.related-product-title')"
        :src="route('shop.api.products.related.index', ['id' => $product->id])"
    />

    <!-- Up-sell Products -->
    <x-shop::products.carousel
        :title="trans('shop::app.products.view.up-sell-title')"
        :src="route('shop.api.products.up-sell.index', ['id' => $product->id])"
    />

    {!! view_render_event('bagisto.shop.products.view.after', ['product' => $product]) !!}

    @pushOnce('scripts')
        <script
            type="text/x-template"
            id="v-product-template"
        >
            <x-shop::form
                v-slot="{ meta, errors, handleSubmit }"
                as="div"
            >
                <form
                    ref="formData"
                    @submit="handleSubmit($event, addToCart)"
                >
                    <input
                        type="hidden"
                        name="product_id"
                        value="{{ $product->id }}"
                    >

                    <input
                        type="hidden"
                        name="is_buy_now"
                        v-model="is_buy_now"
                    >

                    <div class="container px-[60px] max-1180:px-0">
                        <div class="mt-12 flex gap-9 max-1180:flex-wrap max-lg:mt-0 max-sm:gap-y-4">
                            <!-- Gallery Blade Inclusion -->
                            @include('shop::products.view.gallery')

                            <!-- Details -->
                            <div class="relative max-w-[590px] max-1180:w-full max-1180:max-w-full max-1180:px-5 max-sm:px-4">
                                {!! view_render_event('bagisto.shop.products.name.before', ['product' => $product]) !!}

                                <div class="flex justify-between gap-4">
                                    <h1 class="break-all text-3xl font-medium max-sm:text-xl">
                                        {{ $product->name }}
                                    </h1>

                                    @if (core()->getConfigData('customer.settings.wishlist.wishlist_option'))
                                        <div
                                            class="flex max-h-[46px] min-h-[46px] min-w-[46px] cursor-pointer items-center justify-center rounded-full border bg-white text-2xl transition-all hover:opacity-[0.8] max-sm:max-h-7 max-sm:min-h-7 max-sm:min-w-7 max-sm:text-base"
                                            role="button"
                                            aria-label="@lang('shop::app.products.view.add-to-wishlist')"
                                            tabindex="0"
                                            :class="isWishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                                            @click="addToWishlist"
                                        >
                                        </div>
                                    @endif
                                </div>

                                {!! view_render_event('bagisto.shop.products.name.after', ['product' => $product]) !!}

                                <!-- Rating -->
                                {!! view_render_event('bagisto.shop.products.rating.before', ['product' => $product]) !!}

                                @if ($totalRatings = $reviewHelper->getTotalFeedback($product))
                                    <!-- Scroll To Reviews Section and Activate Reviews Tab -->
                                    <div
                                        class="mt-1 w-max cursor-pointer max-sm:mt-1.5"
                                        role="button"
                                        tabindex="0"
                                        @click="scrollToReview"
                                    >
                                        <x-shop::products.ratings
                                            class="transition-all hover:border-gray-400 max-sm:px-3 max-sm:py-1"
                                            :average="$avgRatings"
                                            :total="$totalRatings"
                                            ::rating="true"
                                        />
                                    </div>
                                @endif

                                {!! view_render_event('bagisto.shop.products.rating.after', ['product' => $product]) !!}

                                <!-- Pricing -->
                                {!! view_render_event('bagisto.shop.products.price.before', ['product' => $product]) !!}

                                <div class="mt-[22px] flex items-center gap-2.5 text-2xl !font-medium max-sm:mt-2 max-sm:gap-x-2.5 max-sm:gap-y-0 max-sm:text-lg">
                                    {!! $product->getTypeInstance()->getPriceHtml() !!}
                                </div>

                                @if (\Webkul\Tax\Facades\Tax::isInclusiveTaxProductPrices())
                                    <span class="text-sm font-normal text-zinc-500 max-sm:text-xs">
                                        (@lang('shop::app.products.view.tax-inclusive'))
                                    </span>
                                @endif

                                @if (count($product->getTypeInstance()->getCustomerGroupPricingOffers()))
                                    <div class="mt-2.5 grid gap-1.5">
                                        @foreach ($product->getTypeInstance()->getCustomerGroupPricingOffers() as $offer)
                                            <p class="text-zinc-500 [&>*]:text-black">
                                                {!! $offer !!}
                                            </p>
                                        @endforeach
                                    </div>
                                @endif

                                {!! view_render_event('bagisto.shop.products.price.after', ['product' => $product]) !!}

                                {!! view_render_event('bagisto.shop.products.short_description.before', ['product' => $product]) !!}

                                <p class="mt-6 text-lg text-zinc-500 max-sm:mt-1.5 max-sm:text-sm">
                                    {!! $product->short_description !!}
                                </p>

                                {!! view_render_event('bagisto.shop.products.short_description.after', ['product' => $product]) !!}

                                @include('shop::products.view.types.simple')

                                @include('shop::products.view.types.configurable')

                                @include('shop::products.view.types.grouped')

                                @include('shop::products.view.types.bundle')

                                @include('shop::products.view.types.downloadable')

                                @include('shop::products.view.types.booking')

                                <!-- Product Actions and Quantity Box -->
                                <div class="mt-8 flex max-w-[470px] gap-4 max-sm:mt-4">

                                    {!! view_render_event('bagisto.shop.products.view.quantity.before', ['product' => $product]) !!}

                                    @if ($product->getTypeInstance()->showQuantityBox())
                                        <x-shop::quantity-changer
                                            name="quantity"
                                            value="1"
                                            class="gap-x-4 rounded-xl px-7 py-4 max-md:py-3 max-sm:gap-x-5 max-sm:rounded-lg max-sm:px-4 max-sm:py-1.5"
                                        />
                                    @endif

                                    {!! view_render_event('bagisto.shop.products.view.quantity.after', ['product' => $product]) !!}

                                    @if (core()->getConfigData('sales.checkout.shopping_cart.cart_page'))
                                        <!-- Add To Cart Button -->
                                        {!! view_render_event('bagisto.shop.products.view.add_to_cart.before', ['product' => $product]) !!}

                                        <x-shop::button
                                            type="submit"
                                            class="secondary-button w-full max-w-full max-md:py-3 max-sm:rounded-lg max-sm:py-1.5"
                                            button-type="secondary-button"
                                            :loading="false"
                                            :title="trans('shop::app.products.view.add-to-cart')"
                                            :disabled="! $product->isSaleable(1)"
                                            ::loading="isStoring.addToCart"
                                            ::disabled="isStoring.addToCart"
                                            @click="is_buy_now=0;"
                                        />

                                        {!! view_render_event('bagisto.shop.products.view.add_to_cart.after', ['product' => $product]) !!}
                                    @endif
                                </div>

                                <!-- Buy Now Button -->
                                @if (core()->getConfigData('sales.checkout.shopping_cart.cart_page'))
                                    {!! view_render_event('bagisto.shop.products.view.buy_now.before', ['product' => $product]) !!}

                                    @if (core()->getConfigData('catalog.products.storefront.buy_now_button_display'))
                                        <x-shop::button
                                            type="submit"
                                            class="primary-button mt-5 w-full max-w-[470px] max-md:py-3 max-sm:mt-3 max-sm:rounded-lg max-sm:py-1.5"
                                            button-type="primary-button"
                                            :title="trans('shop::app.products.view.buy-now')"
                                            :disabled="! $product->isSaleable(1)"
                                            ::loading="isStoring.buyNow"
                                            @click="is_buy_now=1;"
                                            ::disabled="isStoring.buyNow"
                                        />
                                    @endif

                                    {!! view_render_event('bagisto.shop.products.view.buy_now.after', ['product' => $product]) !!}
                                @endif

                                {!! view_render_event('bagisto.shop.products.view.additional_actions.before', ['product' => $product]) !!}

                                <!-- Share Buttons -->
                                <div class="mt-10 flex gap-9 max-md:mt-4 max-md:flex-wrap max-sm:justify-center max-sm:gap-3">
                                    {!! view_render_event('bagisto.shop.products.view.compare.before', ['product' => $product]) !!}

            

                                    {!! view_render_event('bagisto.shop.products.view.compare.after', ['product' => $product]) !!}
                                </div>

                                {!! view_render_event('bagisto.shop.products.view.additional_actions.after', ['product' => $product]) !!}
                            </div>
                        </div>
                    </div>
                </form>
            </x-shop::form>
        </script>

        <script type="module">
            app.component('v-product', {
                template: '#v-product-template',

                data() {
                    return {
                        isWishlist: Boolean("{{ (boolean) auth()->guard()->user()?->wishlist_items->where('channel_id', core()->getCurrentChannel()->id)->where('product_id', $product->id)->count() }}"),

                        isCustomer: '{{ auth()->guard('customer')->check() }}',

                        is_buy_now: 0,

                        isStoring: {
                            addToCart: false,

                            buyNow: false,
                        },
                    }
                },

                mounted() {
                    this.$emitter.on('product:swap', this.swapProduct);

                    // 监听浏览器历史返回/前进，依据URL的最后段（url_key）拉取并热切换
                    window.addEventListener('popstate', this.handlePopState);
                },

                beforeUnmount() {
                    this.$emitter.off('product:swap', this.swapProduct);
                    window.removeEventListener('popstate', this.handlePopState);
                },

                methods: {
                    handlePopState() {
                        try {
                            const path = window.location.pathname || '';
                            const segments = path.split('/').filter(Boolean);
                            const last = segments[segments.length - 1] || '';
                            if (!last) return;
                            const urlKey = decodeURIComponent(last);

                            const apiTpl = `{{ route('shop.api.products.view_data', ['urlKey' => '__URL_KEY__']) }}`;
                            const locale = "{{ app()->getLocale() }}";
                            const apiUrl = apiTpl.replace('__URL_KEY__', encodeURIComponent(urlKey)) + `?locale=${encodeURIComponent(locale)}`;
                            console.log(apiUrl);
                            this.$axios.get(apiUrl)
                                .then(({ data }) => {
                                    const payload = data?.data;
                                    if (!payload) return;
                                    this.swapProduct(payload);
                                })
                                .catch(() => {});
                        } catch (e) {}
                    },
                    swapProduct(payload) {
                        try {
                            const form = this.$refs.formData;
                            const idInput = form?.querySelector('input[name="product_id"]');
                            if (idInput) idInput.value = payload.id;

                            // Replace price HTML block
                            const finalPriceEl = document.querySelector('.final-price');
                            const priceWrapper = finalPriceEl ? finalPriceEl.parentElement : null;
                            if (priceWrapper) {
                                const cls = priceWrapper.getAttribute('class') || '';
                                priceWrapper.outerHTML = `<div class="${cls}">${payload.price_html || ''}</div>`;
                            }

                            // Update product name
                            const nameEl = form?.closest('form')?.parentElement?.querySelector('h1');
                            if (nameEl && payload.name) {
                                nameEl.textContent = payload.name;
                            }

                            // Update short description
                            const shortDescEl = document.querySelector('p.mt-6.text-lg.text-zinc-500');
                            if (shortDescEl && payload.short_description) {
                                shortDescEl.innerHTML = payload.short_description;
                            }

                            // Update main description in tabs and accordions
                            const descTabContent = document.querySelector('#descritpion-tab .text-lg.text-zinc-500');
                            if (descTabContent && payload.description) {
                                descTabContent.innerHTML = payload.description;
                            }

                            const descAccordionContent = document.querySelector('.mb-5.text-lg.text-zinc-500');
                            if (descAccordionContent && payload.description) {
                                descAccordionContent.innerHTML = payload.description;
                            }

                            // Update page title
                            const pageTitle = payload.meta_title || payload.name;
                            if (pageTitle) {
                                document.title = pageTitle;
                            }

                            // Update meta description
                            const metaDesc = document.querySelector('meta[name="description"]');
                            if (metaDesc && payload.meta_description) {
                                metaDesc.setAttribute('content', payload.meta_description);
                            }

                            // Update Open Graph meta tags
                            const ogTitle = document.querySelector('meta[property="og:title"]');
                            if (ogTitle && payload.name) {
                                ogTitle.setAttribute('content', payload.name);
                            }

                            const ogDesc = document.querySelector('meta[property="og:description"]');
                            if (ogDesc && payload.description) {
                                const cleanDesc = payload.description.replace(/<[^>]*>/g, '');
                                ogDesc.setAttribute('content', cleanDesc);
                            }

                            // Update Twitter meta tags
                            const twitterTitle = document.querySelector('meta[name="twitter:title"]');
                            if (twitterTitle && payload.name) {
                                twitterTitle.setAttribute('content', payload.name);
                            }

                            const twitterDesc = document.querySelector('meta[name="twitter:description"]');
                            if (twitterDesc && payload.description) {
                                const cleanDesc = payload.description.replace(/<[^>]*>/g, '');
                                twitterDesc.setAttribute('content', cleanDesc);
                            }

                            // Update image meta tags
                            if (payload.base_image && payload.base_image.medium_image_url) {
                                const twitterImage = document.querySelector('meta[name="twitter:image"]');
                                const ogImage = document.querySelector('meta[property="og:image"]');

                                if (twitterImage) {
                                    twitterImage.setAttribute('content', payload.base_image.medium_image_url);
                                }

                                if (ogImage) {
                                    ogImage.setAttribute('content', payload.base_image.medium_image_url);
                                }
                            }

                            // Update gallery via existing bus
                            const images = Array.isArray(payload.gallery) ? payload.gallery : [];
                            if (this.$refs?.gallery?.media) {
                                this.$refs.gallery.media.images = [...images];
                                // 同时更新基础文件显示
                                if (images.length > 0) {
                                    this.$refs.gallery.baseFile.type = 'image';
                                    this.$refs.gallery.baseFile.path = images[0].large_image_url;
                                    this.$refs.gallery.activeIndex = 0;
                                }
                            }
                            this.$emitter.emit('configurable-variant-update-images-event', images);

                            // Update button states based on product saleability
                            const addToCartBtn = document.querySelector('.secondary-button[type="submit"]');
                            const buyNowBtn = document.querySelector('.primary-button[type="submit"]');

                            if (addToCartBtn) {
                                addToCartBtn.disabled = !payload.is_saleable;
                            }
                            if (buyNowBtn) {
                                buyNowBtn.disabled = !payload.is_saleable;
                            }

                            // Update quantity box visibility
                            const quantityBox = document.querySelector('x-shop\\:\\:quantity-changer, .quantity-changer');
                            if (quantityBox) {
                                quantityBox.style.display = payload.show_quantity_box ? 'block' : 'none';
                            }

                            // Notify configurable options to reload with new config
                            this.$emitter.emit('configurable:swap-config', {
                                config: payload.config || {},
                                baseImages: payload.gallery || []
                            });

                            // Reset selected_configurable_option hidden input
                            const selectedConfigInput = form?.querySelector('#selected_configurable_option');
                            if (selectedConfigInput) selectedConfigInput.value = '';

                            // Wishlist visual state if provided
                            if (typeof payload.is_wishlist === 'boolean') {
                                this.isWishlist = payload.is_wishlist;
                            }

                            // Update additional attributes in tabs/accordions if present
                            if (payload.attribute_data && Array.isArray(payload.attribute_data)) {
                                this.updateAdditionalAttributes(payload.attribute_data);
                            }

                            // Update related and up-sell product carousels
                            this.updateProductCarousels(payload.id);

                            // Update canonical URL and Open Graph URL
                            const canonicalLink = document.querySelector('link[rel="canonical"]');
                            const ogUrl = document.querySelector('meta[property="og:url"]');

                            if (payload.url_key) {
                                const newUrl = `{{ route('shop.product_or_category.index', '') }}/${payload.url_key}`;

                                if (canonicalLink) {
                                    canonicalLink.setAttribute('href', newUrl);
                                }

                                if (ogUrl) {
                                    ogUrl.setAttribute('content', newUrl);
                                }
                            }

                            // Update breadcrumb if present
                            const breadcrumbProduct = document.querySelector('.breadcrumb-item:last-child, .breadcrumb li:last-child');
                            if (breadcrumbProduct && payload.name) {
                                const breadcrumbLink = breadcrumbProduct.querySelector('a');
                                const breadcrumbText = breadcrumbProduct.querySelector('span') || breadcrumbProduct;

                                if (breadcrumbLink && payload.url_key) {
                                    breadcrumbLink.textContent = payload.name;
                                    breadcrumbLink.setAttribute('href', `{{ route('shop.product_or_category.index', '') }}/${payload.url_key}`);
                                } else if (breadcrumbText) {
                                    breadcrumbText.textContent = payload.name;
                                }
                            }

                        } catch (e) {
                            console.error('Error in swapProduct:', e);
                        }
                    },

                    updateAdditionalAttributes(attributeData) {
                        try {
                            // Update additional information tab content
                            const infoTabContent = document.querySelector('#information-tab .grid.max-w-max');
                            const infoAccordionContent = document.querySelector('.container.max-1180\\:px-5 .grid.max-w-max');

                            if (attributeData.length === 0) {
                                // Hide additional information sections if no attributes
                                const infoTab = document.querySelector('#information-tab');
                                const infoAccordion = document.querySelector('.container.mt-6 .grid.gap-3 x-shop\\:\\:accordion:nth-child(2)');

                                if (infoTab) infoTab.style.display = 'none';
                                if (infoAccordion) infoAccordion.style.display = 'none';
                                return;
                            }

                            // Generate HTML for attributes
                            let attributeHtml = '';
                            attributeData.forEach(attr => {
                                if (attr.value) {
                                    let valueHtml = '';

                                    if (attr.type === 'file') {
                                        valueHtml = `<a href="/storage/${attr.value}" download="${attr.label}">
                                            <span class="icon-download text-2xl"></span>
                                        </a>`;
                                    } else if (attr.type === 'image') {
                                        valueHtml = `<a href="/storage/${attr.value}" download="${attr.label}">
                                            <img class="h-5 min-h-5 w-5 min-w-5" src="/storage/${attr.value}" alt="Product Image" />
                                        </a>`;
                                    } else if (attr.type === 'multiimage') {
                                        valueHtml = '<p>Multi Image</p>';
                                    } else {
                                        valueHtml = `<div class="grid">
                                            <p class="text-base text-zinc-500">${attr.value || '-'}</p>
                                        </div>`;
                                    }

                                    attributeHtml += `
                                        <div class="grid">
                                            <p class="text-base text-black">${attr.label}</p>
                                        </div>
                                        ${valueHtml}
                                    `;
                                }
                            });

                            // Update tab content
                            if (infoTabContent) {
                                infoTabContent.innerHTML = attributeHtml;
                            }

                            // Update accordion content
                            if (infoAccordionContent) {
                                infoAccordionContent.innerHTML = attributeHtml;
                            }

                            // Show additional information sections
                            const infoTab = document.querySelector('#information-tab');
                            const infoAccordion = document.querySelector('.container.mt-6 .grid.gap-3 x-shop\\:\\:accordion:nth-child(2)');

                            if (infoTab) infoTab.style.display = 'block';
                            if (infoAccordion) infoAccordion.style.display = 'block';

                        } catch (e) {
                            console.error('Error updating additional attributes:', e);
                        }
                    },

                    updateProductCarousels(productId) {
                        try {
                            // Update related products carousel
                            const relatedCarousel = document.querySelector('x-shop\\:\\:products\\.carousel[title*="related"], .products-carousel.related');
                            if (relatedCarousel) {
                                const relatedUrl = `{{ route('shop.api.products.related.index', ['id' => '__PRODUCT_ID__']) }}`.replace('__PRODUCT_ID__', productId);
                                if (relatedCarousel.hasAttribute('src')) {
                                    relatedCarousel.setAttribute('src', relatedUrl);
                                } else if (relatedCarousel.dataset && relatedCarousel.dataset.src) {
                                    relatedCarousel.dataset.src = relatedUrl;
                                }
                            }

                            // Update up-sell products carousel
                            const upSellCarousel = document.querySelector('x-shop\\:\\:products\\.carousel[title*="up-sell"], .products-carousel.up-sell');
                            if (upSellCarousel) {
                                const upSellUrl = `{{ route('shop.api.products.up-sell.index', ['id' => '__PRODUCT_ID__']) }}`.replace('__PRODUCT_ID__', productId);
                                if (upSellCarousel.hasAttribute('src')) {
                                    upSellCarousel.setAttribute('src', upSellUrl);
                                } else if (upSellCarousel.dataset && upSellCarousel.dataset.src) {
                                    upSellCarousel.dataset.src = upSellUrl;
                                }
                            }

                            // Trigger carousel refresh if they have refresh methods
                            if (window.refreshProductCarousels) {
                                window.refreshProductCarousels();
                            }

                        } catch (e) {
                            console.error('Error updating product carousels:', e);
                        }
                    },

                    addToCart(params) {
                        const operation = this.is_buy_now ? 'buyNow' : 'addToCart';

                        this.isStoring[operation] = true;

                        let formData = new FormData(this.$refs.formData);

                        this.ensureQuantity(formData);

                        this.$axios.post('{{ route("shop.api.checkout.cart.store") }}', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                }
                            })
                            .then(response => {
                                if (response.data.message) {
                                    this.$emitter.emit('update-mini-cart', response.data.data);

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                    if (response.data.redirect) {
                                        window.location.href= response.data.redirect;
                                    }
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isStoring[operation] = false;
                            })
                            .catch(error => {
                                this.isStoring[operation] = false;

                                this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.message });
                            });
                    },

                    addToWishlist() {
                        if (this.isCustomer) {
                            this.$axios.post('{{ route('shop.api.customers.account.wishlist.store') }}', {
                                    product_id: "{{ $product->id }}"
                                })
                                .then(response => {
                                    this.isWishlist = ! this.isWishlist;

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                                })
                                .catch(error => {});
                        } else {
                            window.location.href = "{{ route('shop.customer.session.index')}}";
                        }
                    },

                    addToCompare(productId) {
                        /**
                         * This will handle for customers.
                         */
                        if (this.isCustomer) {
                            this.$axios.post('{{ route("shop.api.compare.store") }}', {
                                    'product_id': productId
                                })
                                .then(response => {
                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                                })
                                .catch(error => {
                                    if ([400, 422].includes(error.response.status)) {
                                        this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.data.message });

                                        return;
                                    }

                                    this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message});
                                });

                            return;
                        }

                        /**
                         * This will handle for guests.
                         */
                        let existingItems = this.getStorageValue(this.getCompareItemsStorageKey()) ?? [];

                        if (existingItems.length) {
                            if (! existingItems.includes(productId)) {
                                existingItems.push(productId);

                                this.setStorageValue(this.getCompareItemsStorageKey(), existingItems);

                                this.$emitter.emit('add-flash', { type: 'success', message: "@lang('shop::app.products.view.add-to-compare')" });
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: "@lang('shop::app.products.view.already-in-compare')" });
                            }
                        } else {
                            this.setStorageValue(this.getCompareItemsStorageKey(), [productId]);

                            this.$emitter.emit('add-flash', { type: 'success', message: "@lang('shop::app.products.view.add-to-compare')" });
                        }
                    },

                    updateQty(quantity, id) {
                        this.isLoading = true;

                        let qty = {};

                        qty[id] = quantity;

                        this.$axios.put('{{ route('shop.api.checkout.cart.update') }}', { qty })
                            .then(response => {
                                if (response.data.message) {
                                    this.cart = response.data.data;
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isLoading = false;
                            }).catch(error => this.isLoading = false);
                    },

                    getCompareItemsStorageKey() {
                        return 'compare_items';
                    },

                    setStorageValue(key, value) {
                        localStorage.setItem(key, JSON.stringify(value));
                    },

                    getStorageValue(key) {
                        let value = localStorage.getItem(key);

                        if (value) {
                            value = JSON.parse(value);
                        }

                        return value;
                    },

                    scrollToReview() {
                        let accordianElement = document.querySelector('#review-accordian-button');

                        if (accordianElement) {
                            accordianElement.click();

                            accordianElement.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }

                        let tabElement = document.querySelector('#review-tab-button');

                        if (tabElement) {
                            tabElement.click();

                            tabElement.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    },

                    ensureQuantity(formData) {
                        if (! formData.has('quantity')) {
                            formData.append('quantity', 1);
                        }
                    },
                },
            });
        </script>

        <style>
            /* Enhanced Accordion Styling */
            .accordion-custom {
                transition: all 0.3s ease-in-out;
            }
            
            .accordion-custom:hover {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            }
            
            /* Unified Accordion Styling with separator line */
            .accordion-top {
                border-bottom: none !important;
                border-bottom-left-radius: 0 !important;
                border-bottom-right-radius: 0 !important;
                margin-bottom: 0;
            }
            
            .accordion-bottom {
                border-top: none !important;
                border-top-left-radius: 0 !important;
                border-top-right-radius: 0 !important;
                margin-top: 0;
            }
            
            /* Separator line styling */
            .accordion-custom + div {
                /* Separator line between accordions */
                margin: 0;
                border-color: #e5e7eb;
            }
            
            .accordion-custom + div + .accordion-custom {
                /* Additional Information accordion styling */
                border-top: none;
            }
            
            /* Arrow icon styling with enhanced animation */
            .accordion-custom .icon-arrow-up,
            .accordion-custom .icon-arrow-down {
                font-size: 1.2rem;
                color: #6b7280;
                transition: all 0.3s ease-in-out;
            }
            
            .accordion-custom .icon-arrow-up:hover,
            .accordion-custom .icon-arrow-down:hover {
                color: #374151;
                transform: scale(1.1);
            }
            
            /* Smooth content reveal animation */
            .accordion-custom [v-show] {
                animation: slideDown 0.3s ease-out;
            }
            
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                    max-height: 0;
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                    max-height: 1000px;
                }
            }
            
            /* Content styling improvements */
            .accordion-custom .text-lg {
                line-height: 1.7;
            }
            
            /* Two-column layout specific improvements */
            .product-info-sidebar {
                position: sticky;
                top: 20px;
                height: fit-content;
            }
            
            /* Card hover effects */
            .info-card {
                transition: all 0.3s ease;
                transform: translateY(0);
            }
            
            .info-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }
            
            /* Icon styling improvements */
            .feature-icon {
                transition: transform 0.2s ease;
            }
            
            .info-card:hover .feature-icon {
                transform: scale(1.1);
            }
            
            /* Responsive improvements */
            @media (max-width: 1024px) {
                .product-info-sidebar {
                    position: static;
                }
            }
            
            @media (max-width: 768px) {
                .accordion-custom .text-lg {
                    font-size: 1rem;
                }
                
                .accordion-custom [class*="!px-6"] {
                    padding-left: 1rem !important;
                    padding-right: 1rem !important;
                }
                
                .accordion-custom [class*="!py-4"] {
                    padding-top: 0.75rem !important;
                    padding-bottom: 0.75rem !important;
                }
                
                /* Mobile card improvements */
                .info-card {
                    padding: 1rem !important;
                }
                
                .info-card h3 {
                    font-size: 1rem !important;
                    margin-bottom: 0.75rem !important;
                }
                
                .info-card .text-sm {
                    font-size: 0.875rem !important;
                }
                
                .info-card .space-y-3 > * + * {
                    margin-top: 0.5rem !important;
                }
                
                .info-card .space-y-4 > * + * {
                    margin-top: 0.75rem !important;
                }
            }
        </style>
    @endPushOnce
</x-shop::layouts>
