{"__meta": {"id": "01K40CP591KTY7TH3DDY9B1A5K", "datetime": "2025-08-31 16:42:13", "utime": **********.281638, "method": "GET", "uri": "/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.085679, "end": **********.291256, "duration": 0.20557689666748047, "duration_str": "206ms", "measures": [{"label": "Booting", "start": **********.085679, "relative_start": 0, "end": **********.254985, "relative_end": **********.254985, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.255005, "relative_start": 0.****************, "end": **********.291258, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "36.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.269772, "relative_start": 0.*****************, "end": **********.275215, "relative_end": **********.275215, "duration": 0.*****************, "duration_str": "5.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.280297, "relative_start": 0.*****************, "end": **********.280388, "relative_end": **********.280388, "duration": 9.107589721679688e-05, "duration_str": "91μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.280402, "relative_start": 0.*****************, "end": **********.280414, "relative_end": **********.280414, "duration": 1.2159347534179688e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "206ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1914530705 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1914530705\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1945796583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1945796583\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-267115229 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"749 characters\">cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IjZ3bHpxWEpBd2txZHBjZmU4UVcwQkE9PSIsInZhbHVlIjoiNGw4NU9xbjNFVkwwVUZ6MmZ5Y1FYRU5icmZTSmhUbzNVS09EYi8rNFh4WTdDcFV6Nmh1S2VVWnNGZTMyS2FGTStQRTJiT1hpMFRBcm5QZ2U3MGdGQnJaMnA2QXNoWmRmVmpiWG1JcDg1ai81VGhSZDhvOFBUUDlmMDU0Q3NHa0ciLCJtYWMiOiI1ZGI2MjIzN2VmMjQyODkwYmJhZDE5MjkyMmIyYWQzODk4N2JiN2U5NzEwOTFhYjgyYjUxNDE4MTVhNzZmMmZhIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjNTdklodVphQUtITTlzdW9ZV2dPU0E9PSIsInZhbHVlIjoiMW1RUUl6anNaeERFNUtkN0hwZEQwY29PaDZIcEdqcU5jenAxbFY1NlJCU2diRjY3Q0JXMXBnTEpHRkJtY1gydURCYkRnME5DQktSaWZEdFd6aGtaS2dZU3V0VXBKOWZVV3ZDVEF4MlA2U1FrWEtrcnJDOU4xei9RaXY1Tnpzc3giLCJtYWMiOiI0MmNhZjI1ZDE2ODZlNDJkNWQ2YjhlMDBmNDdmOTE0ODA3YTUzYTRmMDRjMWE1ODM0ZmY1ZDY5NzIwOTQ1MDk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267115229\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2137987953 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZ3bHpxWEpBd2txZHBjZmU4UVcwQkE9PSIsInZhbHVlIjoiNGw4NU9xbjNFVkwwVUZ6MmZ5Y1FYRU5icmZTSmhUbzNVS09EYi8rNFh4WTdDcFV6Nmh1S2VVWnNGZTMyS2FGTStQRTJiT1hpMFRBcm5QZ2U3MGdGQnJaMnA2QXNoWmRmVmpiWG1JcDg1ai81VGhSZDhvOFBUUDlmMDU0Q3NHa0ciLCJtYWMiOiI1ZGI2MjIzN2VmMjQyODkwYmJhZDE5MjkyMmIyYWQzODk4N2JiN2U5NzEwOTFhYjgyYjUxNDE4MTVhNzZmMmZhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjNTdklodVphQUtITTlzdW9ZV2dPU0E9PSIsInZhbHVlIjoiMW1RUUl6anNaeERFNUtkN0hwZEQwY29PaDZIcEdqcU5jenAxbFY1NlJCU2diRjY3Q0JXMXBnTEpHRkJtY1gydURCYkRnME5DQktSaWZEdFd6aGtaS2dZU3V0VXBKOWZVV3ZDVEF4MlA2U1FrWEtrcnJDOU4xei9RaXY1Tnpzc3giLCJtYWMiOiI0MmNhZjI1ZDE2ODZlNDJkNWQ2YjhlMDBmNDdmOTE0ODA3YTUzYTRmMDRjMWE1ODM0ZmY1ZDY5NzIwOTQ1MDk4IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137987953\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2098672007 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1986</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">1f3427cbbd4ecbda6a1f2680b426de92</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 15:42:13 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098672007\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-583785969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-583785969\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}