<v-categories-carousel
    src="{{ $src }}"
    title="{{ $title }}"
    navigation-link="{{ $navigationLink ?? '' }}">
    <x-shop::shimmer.categories.carousel
        :count="8"
        :navigation-link="$navigationLink ?? false" />
</v-categories-carousel>

@pushOnce('scripts')
<script
    type="text/x-template"
    id="v-categories-carousel-template">
    <div
            class="w-full mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4"
            v-if="! isLoading && categories?.length"
        >
             <div class="flex-col items-center justify-center">
                <h2 class="font-dmserif text-3xl max-md:text-2xl max-sm:text-xl" style="font-family: 'AlimamaShuHeiTi-Bold', 'DM Serif Display', serif; letter-spacing: 1.6px; text-align: center;">
                    @lang('shop::app.products.view.featured-collections.name')
                </h2>
            <div class="relative mt-10">
                <div
                    ref="swiperContainer"
                    class="scrollbar-hide flex overflow-auto scroll-smooth px-[38px] gap-[38px]"
                >
                    <div
                        class="flex-shrink-0 flex flex-col items-center justify-between p-6 rounded-lg bg-zinc-100 font-medium category-card"
                        v-for="category in categories"
                    >
                    <div class="flex-1">
                        <a
                            :href="category.slug"
                            class="flex-shrink-0 max-md:w-16 max-md:h-16 max-sm:w-14 max-sm:h-14"
                            :aria-label="category.name"
                        >
                            <x-shop::media.images.lazy
                                ::src="category.logo?.large_image_url || '{{ bagisto_asset('images/small-product-placeholder.webp') }}'"
                                class="w-full h-full object-contain"
                                ::alt="category.name"
                            />
                        </a>
</div>
                        <a
                            :href="category.slug"
                            class="flex-shrink-0 mt-2"
                            >
                            <p
                                class="text-center text-base text-black max-md:text-sm max-md:font-normal max-sm:text-xs"
                                v-text="category.name"
                            >
                            </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Carousel Shimmer -->
        <template v-if="isLoading">
            <x-shop::shimmer.categories.carousel
                :count="8"
                :navigation-link="$navigationLink ?? false"
            />
        </template>
    </div>
    </script>

<script type="module">
    app.component('v-categories-carousel', {
        template: '#v-categories-carousel-template',

        props: [
            'src',
            'title',
            'navigationLink',
        ],

        data() {
            return {
                isLoading: true,

                categories: [],

                offset: 400,
            };
        },

        mounted() {
            this.getCategories();
        },

        methods: {
            getCategories() {
                this.$axios.get(this.src)
                    .then(response => {
                        this.isLoading = false;

                        this.categories = response.data.data;
                    }).catch(error => {
                        console.log(error);
                    });
            },

            swipeLeft() {
                const container = this.$refs.swiperContainer;

                container.scrollLeft -= this.offset;
            },

            swipeRight() {
                const container = this.$refs.swiperContainer;

                container.scrollLeft += this.offset;
            },
        },
    });
</script>

<style>
@media (max-width: 767px) {
    .category-card {
        width: calc((100vw - 5 * 28px) / 2) !important;
        height: calc((100vw - 5 * 28px) / 2) !important;
    }
}

@media (min-width: 1280px) {
    .category-card {
        width: calc((100vw - 5 * 38px) / 4) !important;
        height: calc((100vw - 5 * 38px) / 4) !important;
    }
}
</style>
@endPushOnce