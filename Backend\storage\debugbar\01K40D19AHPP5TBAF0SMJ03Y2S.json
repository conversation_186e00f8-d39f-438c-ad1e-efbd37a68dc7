{"__meta": {"id": "01K40D19AHPP5TBAF0SMJ03Y2S", "datetime": "2025-08-31 16:48:17", "utime": **********.87424, "method": "GET", "uri": "/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.692018, "end": **********.884025, "duration": 0.19200706481933594, "duration_str": "192ms", "measures": [{"label": "Booting", "start": **********.692018, "relative_start": 0, "end": **********.852803, "relative_end": **********.852803, "duration": 0.*****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.852816, "relative_start": 0.****************, "end": **********.884027, "relative_end": 1.9073486328125e-06, "duration": 0.031210899353027344, "duration_str": "31.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.863219, "relative_start": 0.*****************, "end": **********.866574, "relative_end": **********.866574, "duration": 0.0033550262451171875, "duration_str": "3.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.872733, "relative_start": 0.*****************, "end": **********.872825, "relative_end": **********.872825, "duration": 9.179115295410156e-05, "duration_str": "92μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.872838, "relative_start": 0.*****************, "end": **********.872849, "relative_end": **********.872849, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "193ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-813181352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-813181352\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1104594135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1104594135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1479706185 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"749 characters\">cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IjZmb25LbEtid1JveGRwRkhzdi80cFE9PSIsInZhbHVlIjoiODBId0lSMFhrKy8xYnp0Zk5LdUpET3NRZXlobjdoYTZqWGtWT1ZBRGIrRE9yU3lucitVTmtTNjROMzAyZ016ak5QV1Z0SkpZNFMwQSt3WGZ3SGp4bW84TkpRRWIwaFl0RnMzWUtpenVlM3EydTdzRkYyT3BHVHhtY0Vsck1KWjAiLCJtYWMiOiIwNjYwNjRmYzEyYzQ0MWUzM2VjNjYwNGNkYzQ0ZjMzNDFkZDExYWJhZTBkZjJiYzIwZTE2YWIyMjIwYjY4M2FiIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjJ2V3RvQ1kzNlgvb0Y3MHlxOUhHSHc9PSIsInZhbHVlIjoibmJrdGVxRWdjTFNjMmwrcVlkazVQeWYvU1BqVklmc09NM2xTNUgyNTZPbmlWQ2Y3Q1Bvdmp3M25RRWgzZUR1b2l2QXpKMzJwODhNNUkyVlk1dE9vb2lFQkdUME56SG5mTCt1N2xxWnRtV3NFYXRFdUpFVUpFbWRMZXpVRWkrMmsiLCJtYWMiOiJjMjEwNTFmMWMxMzljZmM0NzU4N2E1NjZkYWRkMDM3ZGNjNzYyMzMwYTEzOWZjYzA0ODNkYjFkNzRkZWU0NmU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479706185\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1592672614 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZmb25LbEtid1JveGRwRkhzdi80cFE9PSIsInZhbHVlIjoiODBId0lSMFhrKy8xYnp0Zk5LdUpET3NRZXlobjdoYTZqWGtWT1ZBRGIrRE9yU3lucitVTmtTNjROMzAyZ016ak5QV1Z0SkpZNFMwQSt3WGZ3SGp4bW84TkpRRWIwaFl0RnMzWUtpenVlM3EydTdzRkYyT3BHVHhtY0Vsck1KWjAiLCJtYWMiOiIwNjYwNjRmYzEyYzQ0MWUzM2VjNjYwNGNkYzQ0ZjMzNDFkZDExYWJhZTBkZjJiYzIwZTE2YWIyMjIwYjY4M2FiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJ2V3RvQ1kzNlgvb0Y3MHlxOUhHSHc9PSIsInZhbHVlIjoibmJrdGVxRWdjTFNjMmwrcVlkazVQeWYvU1BqVklmc09NM2xTNUgyNTZPbmlWQ2Y3Q1Bvdmp3M25RRWgzZUR1b2l2QXpKMzJwODhNNUkyVlk1dE9vb2lFQkdUME56SG5mTCt1N2xxWnRtV3NFYXRFdUpFVUpFbWRMZXpVRWkrMmsiLCJtYWMiOiJjMjEwNTFmMWMxMzljZmM0NzU4N2E1NjZkYWRkMDM3ZGNjNzYyMzMwYTEzOWZjYzA0ODNkYjFkNzRkZWU0NmU2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592672614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1708590930 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fe98aceff7b794c22782ef99966c688a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 15:48:17 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708590930\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2146192803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2146192803\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}