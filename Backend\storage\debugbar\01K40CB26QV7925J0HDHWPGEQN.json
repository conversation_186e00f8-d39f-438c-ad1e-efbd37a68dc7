{"__meta": {"id": "01K40CB26QV7925J0HDHWPGEQN", "datetime": "2025-08-31 16:36:09", "utime": **********.688122, "method": "GET", "uri": "/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.483991, "end": **********.697879, "duration": 0.21388816833496094, "duration_str": "214ms", "measures": [{"label": "Booting", "start": **********.483991, "relative_start": 0, "end": **********.665915, "relative_end": **********.665915, "duration": 0.*****************, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.665932, "relative_start": 0.*****************, "end": **********.697881, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "31.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.678231, "relative_start": 0.*****************, "end": **********.681913, "relative_end": **********.681913, "duration": 0.0036818981170654297, "duration_str": "3.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.686523, "relative_start": 0.*****************, "end": **********.686665, "relative_end": **********.686665, "duration": 0.00014209747314453125, "duration_str": "142μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.686686, "relative_start": 0.*****************, "end": **********.686702, "relative_end": **********.686702, "duration": 1.5974044799804688e-05, "duration_str": "16μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "214ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1894855759 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1894855759\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-46600534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-46600534\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1341195550 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"749 characters\">cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6InRqSXNCTHBtVEZDZHVObzVtdW5jcGc9PSIsInZhbHVlIjoiYTdxeDl2N3F6VVUvZTZlTktIelBqdExFVHEvZHZxMlY0MXR2Q0FtTWRoWWIzNTQzSFhQYnVDVHBOYi9mK3BSMTFMTENQaDJIaHg1MGVGcS9BdFAvLzN0bmlzSzVyU0hPUlBHdWlaL25QT2NhR240U1JjTmtMbGNFSXlNcUpvQjEiLCJtYWMiOiI2MTg3NTIxMzY0NTU2ZmMyMGRjNGVlMjMyMWFlNzQ2N2E5NjQ5ZmMxZTA2ZGNkZmIxMjZhNWQ4ZjE1NjNkZGJkIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ill0dWR6S2JoaXNBTVBTSVBCZXRNN2c9PSIsInZhbHVlIjoiRVM3eW1VR2dSL1JtbTA4UFlnOVppMHlzelpySGY2ckRvOHF4QU9EVStlU3dydzUwYThBZkNNVXY2Vm1xODRhOGhlc1REeGZWcS9XVUYrcXF4alVmZWVDcTFucUxNNEpaaExxVHpKenNvRGJHcnU4cWY3dnhxa1lEVHhHRnlFd08iLCJtYWMiOiIwZTY4YjgwZGQ0MjA0NDJmYzk3MjUyYjVhNDQ2MTQ4ODgzNDIzMGI5NzM1NDM3ZjU0ZTcwZTUxNTA3Njg0ZGEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341195550\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-755322503 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InRqSXNCTHBtVEZDZHVObzVtdW5jcGc9PSIsInZhbHVlIjoiYTdxeDl2N3F6VVUvZTZlTktIelBqdExFVHEvZHZxMlY0MXR2Q0FtTWRoWWIzNTQzSFhQYnVDVHBOYi9mK3BSMTFMTENQaDJIaHg1MGVGcS9BdFAvLzN0bmlzSzVyU0hPUlBHdWlaL25QT2NhR240U1JjTmtMbGNFSXlNcUpvQjEiLCJtYWMiOiI2MTg3NTIxMzY0NTU2ZmMyMGRjNGVlMjMyMWFlNzQ2N2E5NjQ5ZmMxZTA2ZGNkZmIxMjZhNWQ4ZjE1NjNkZGJkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ill0dWR6S2JoaXNBTVBTSVBCZXRNN2c9PSIsInZhbHVlIjoiRVM3eW1VR2dSL1JtbTA4UFlnOVppMHlzelpySGY2ckRvOHF4QU9EVStlU3dydzUwYThBZkNNVXY2Vm1xODRhOGhlc1REeGZWcS9XVUYrcXF4alVmZWVDcTFucUxNNEpaaExxVHpKenNvRGJHcnU4cWY3dnhxa1lEVHhHRnlFd08iLCJtYWMiOiIwZTY4YjgwZGQ0MjA0NDJmYzk3MjUyYjVhNDQ2MTQ4ODgzNDIzMGI5NzM1NDM3ZjU0ZTcwZTUxNTA3Njg0ZGEyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755322503\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-66979851 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1986</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">1f3427cbbd4ecbda6a1f2680b426de92</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 15:36:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66979851\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1415758242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1415758242\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/253/IA4jD7OPzOpGf0VhFGVPWKE4tgQwvutMb3eUQUlG.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}