<!-- SEO Meta Content -->
@push('meta')
    <meta
        name="description"
        content="Join MLKPULS - Premium Mobile Accessories Wholesale Partner in Europe. High-quality phone cases and digital accessories for retailers."
    />

    <meta
        name="keywords"
        content="MLKPULS, wholesale, mobile accessories, phone cases, digital accessories, Europe, Italy, premium quality"
    />
@endPush

@push('styles')
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .stats-counter {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
        }
        .video-overlay {
            background: linear-gradient(45deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
        }
    </style>
@endPush

<x-shop::layouts
    :has-header="false"
    :has-feature="false"
    :has-footer="false"
>
    <x-slot:title>
        MLKPULS Wholesale Partnership - Premium Mobile Accessories
    </x-slot:title>

    <!-- Hero Section -->
    <div class="hero-gradient relative min-h-screen overflow-hidden">
        <!-- Navigation -->
        <nav class="relative z-10 flex items-center justify-between px-6 py-4 lg:px-12">
            <a href="{{ route('shop.home.index') }}" class="flex items-center">
                <img
                    src="{{ core()->getCurrentChannel()->logo_url ?? bagisto_asset('images/logo.svg') }}"
                    alt="MLKPULS"
                    class="h-10 w-auto"
                >
            </a>
            <div class="hidden md:flex items-center space-x-8 text-white">
                <a href="#about" class="hover:text-blue-200 transition-colors">About Us</a>
                <a href="#benefits" class="hover:text-blue-200 transition-colors">Benefits</a>
                <a href="#register" class="hover:text-blue-200 transition-colors">Register</a>
            </div>
        </nav>

        <!-- Hero Content -->
        <div class="relative z-10 flex items-center min-h-[calc(100vh-80px)] px-6 lg:px-12">
            <div class="max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
                <div class="text-white">
                    <h1 class="text-5xl lg:text-7xl font-bold leading-tight mb-6">
                        Partner with
                        <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                            MLKPULS
                        </span>
                    </h1>
                    <p class="text-xl lg:text-2xl mb-8 text-blue-100 leading-relaxed">
                        Europe's leading premium mobile accessories brand. Join our exclusive wholesale network and unlock exceptional opportunities in Italy and beyond.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 mb-12">
                        <a href="#register" class="bg-white text-blue-900 px-8 py-4 rounded-full font-semibold text-lg hover:bg-blue-50 transition-colors inline-flex items-center justify-center">
                            Become a Partner
                            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                        <a href="#video" class="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-blue-900 transition-colors inline-flex items-center justify-center">
                            Watch Our Story
                            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a2 2 0 012-2h8a2 2 0 012 2v2M9 18h6"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-8">
                        <div class="text-center">
                            <div class="stats-counter text-3xl lg:text-4xl font-bold text-yellow-400">500+</div>
                            <div class="text-blue-200 text-sm lg:text-base">Partners</div>
                        </div>
                        <div class="text-center">
                            <div class="stats-counter text-3xl lg:text-4xl font-bold text-yellow-400">50K+</div>
                            <div class="text-blue-200 text-sm lg:text-base">Products</div>
                        </div>
                        <div class="text-center">
                            <div class="stats-counter text-3xl lg:text-4xl font-bold text-yellow-400">15+</div>
                            <div class="text-blue-200 text-sm lg:text-base">Countries</div>
                        </div>
                    </div>
                </div>

                <!-- Hero Image -->
                <div class="relative">
                    <div class="relative z-10">
                        <img src="/images/hero-products.jpg" alt="MLKPULS Premium Products" class="w-full h-auto rounded-2xl shadow-2xl">
                    </div>
                    <!-- Floating elements -->
                    <div class="absolute -top-4 -right-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
                    <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-orange-500 rounded-full opacity-20 animate-pulse delay-1000"></div>
                </div>
            </div>
        </div>

        <!-- Background decorations -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div class="absolute top-20 left-10 w-64 h-64 bg-blue-400 rounded-full opacity-10 animate-pulse"></div>
            <div class="absolute bottom-20 right-10 w-96 h-96 bg-purple-400 rounded-full opacity-10 animate-pulse delay-500"></div>
        </div>
    </div>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-6 lg:px-12">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">About MLKPULS</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Leading the European market in premium mobile accessories with innovative design, superior quality, and exceptional service.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <div>
                    <h3 class="text-3xl font-bold text-gray-900 mb-6">Our Story</h3>
                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        Founded with a vision to revolutionize mobile accessories, MLKPULS has become Europe's most trusted brand for premium phone cases and digital accessories. Our commitment to quality and innovation has made us the preferred choice for retailers across Italy and beyond.
                    </p>
                    <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                        We combine cutting-edge technology with elegant design to create products that not only protect but enhance the mobile experience. Our extensive catalog features over 50,000 products designed for every major smartphone brand.
                    </p>

                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-xl shadow-lg">
                            <div class="text-3xl font-bold text-blue-600 mb-2">2018</div>
                            <div class="text-gray-600">Founded</div>
                        </div>
                        <div class="bg-white p-6 rounded-xl shadow-lg">
                            <div class="text-3xl font-bold text-blue-600 mb-2">€50M+</div>
                            <div class="text-gray-600">Annual Revenue</div>
                        </div>
                    </div>
                </div>

                <div class="relative">
                    <img src="/images/about-mlkpuls.jpg" alt="MLKPULS Team" class="w-full h-auto rounded-2xl shadow-2xl">
                    <div class="absolute -bottom-6 -right-6 bg-blue-600 text-white p-6 rounded-xl shadow-xl">
                        <div class="text-2xl font-bold">Premium Quality</div>
                        <div class="text-blue-200">Guaranteed</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Video Section -->
    <section id="video" class="py-20 bg-gray-900 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-6 lg:px-12">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">See MLKPULS in Action</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Discover how our premium accessories are transforming the mobile experience across Europe.
                </p>
            </div>

            <div class="relative max-w-4xl mx-auto">
                <div class="relative aspect-video rounded-2xl overflow-hidden shadow-2xl">
                    <div class="video-overlay absolute inset-0 flex items-center justify-center cursor-pointer group" onclick="playVideo()">
                        <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-8 group-hover:bg-opacity-30 transition-all duration-300">
                            <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                        </div>
                    </div>
                    <img src="/images/video-thumbnail.jpg" alt="MLKPULS Video" class="w-full h-full object-cover">
                    <video id="promotional-video" class="w-full h-full object-cover hidden" controls>
                        <source src="/videos/mlkpuls-story.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6 lg:px-12">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">Why Choose MLKPULS</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Join Europe's fastest-growing mobile accessories network and unlock exclusive benefits for your business.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="feature-card bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Premium Quality</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Every product undergoes rigorous quality testing. We guarantee premium materials and superior craftsmanship in every accessory.
                    </p>
                </div>

                <div class="feature-card bg-gradient-to-br from-green-50 to-emerald-100 p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Fast Delivery</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Express shipping across Europe with 24-48 hour delivery. Our logistics network ensures your orders arrive on time, every time.
                    </p>
                </div>

                <div class="feature-card bg-gradient-to-br from-purple-50 to-violet-100 p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Competitive Pricing</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Exclusive wholesale pricing with volume discounts. Maximize your profit margins with our competitive rate structure.
                    </p>
                </div>

                <div class="feature-card bg-gradient-to-br from-orange-50 to-amber-100 p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-orange-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">24/7 Support</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Dedicated account managers and round-the-clock customer support. We're here to help your business succeed.
                    </p>
                </div>

                <div class="feature-card bg-gradient-to-br from-red-50 to-rose-100 p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-red-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Marketing Support</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Professional marketing materials, product photography, and promotional campaigns to boost your sales.
                    </p>
                </div>

                <div class="feature-card bg-gradient-to-br from-teal-50 to-cyan-100 p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-teal-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Analytics Dashboard</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Advanced analytics and reporting tools to track your performance, inventory, and sales trends in real-time.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Section -->
    <section id="register" class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-6 lg:px-12">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">Join Our Partner Network</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Complete the form below to start your journey with MLKPULS. Our team will review your application and contact you within 24 hours.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-16 items-start">
                <!-- Registration Form -->
                <div class="bg-white rounded-2xl shadow-2xl p-8 lg:p-12">
                    <h3 class="text-3xl font-bold text-gray-900 mb-8">Partner Registration</h3>
                <x-shop::form :action="route('shop.customers.register.wholesale.store')">
                    <!-- Base required fields: name + email + password -->
                    <div class="grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label class="required">
                                @lang('shop::app.customers.signup-form.first-name')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="text"
                                class="px-6 py-4"
                                name="first_name"
                                rules="required"
                                :value="old('first_name')"
                                :label="trans('shop::app.customers.signup-form.first-name')"
                                :placeholder="trans('shop::app.customers.signup-form.first-name')"
                            />
                            <x-shop::form.control-group.error control-name="first_name" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label class="required">
                                @lang('shop::app.customers.signup-form.last-name')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="text"
                                class="px-6 py-4"
                                name="last_name"
                                rules="required"
                                :value="old('last_name')"
                                :label="trans('shop::app.customers.signup-form.last-name')"
                                :placeholder="trans('shop::app.customers.signup-form.last-name')"
                            />
                            <x-shop::form.control-group.error control-name="last_name" />
                        </x-shop::form.control-group>
                    </div>

                    <div class="grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label class="required">
                                @lang('shop::app.customers.signup-form.email')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="email"
                                class="px-6 py-4"
                                name="email"
                                rules="required|email"
                                :value="old('email')"
                                :label="trans('shop::app.customers.signup-form.email')"
                                placeholder="<EMAIL>"
                            />
                            <x-shop::form.control-group.error control-name="email" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label class="required">
                                @lang('shop::app.customers.signup-form.password')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="password"
                                class="px-6 py-4"
                                name="password"
                                rules="required|min:6"
                                :label="trans('shop::app.customers.signup-form.password')"
                                ref="password"
                            />
                            <x-shop::form.control-group.error control-name="password" />
                        </x-shop::form.control-group>
                    </div>

                    <div class="grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.confirm-pass')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="password"
                                class="px-6 py-4"
                                name="password_confirmation"
                                rules="confirmed:@password"
                                :label="trans('shop::app.customers.signup-form.password')"
                            />
                            <x-shop::form.control-group.error control-name="password_confirmation" />
                        </x-shop::form.control-group>
                    </div>

                    <!-- Optional common fields -->
                    <div class="grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.phone')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="text"
                                class="px-6 py-4"
                                name="phone"
                                :value="old('phone')"
                                :label="trans('shop::app.customers.signup-form.phone')"
                            />
                            <x-shop::form.control-group.error control-name="phone" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.social-title')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control
                                type="text"
                                class="px-6 py-4"
                                name="social_title"
                                :value="old('social_title')"
                                :label="trans('shop::app.customers.signup-form.social-title')"
                            />
                            <x-shop::form.control-group.error control-name="social_title" />
                        </x-shop::form.control-group>
                    </div>

                    <div class="grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.gender')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="select" name="gender">
                                <option value="">@lang('shop::app.components.common.select')</option>
                                <option value="male" @selected(old('gender')==='male')>@lang('shop::app.customers.signup-form.gender-options.male')</option>
                                <option value="female" @selected(old('gender')==='female')>@lang('shop::app.customers.signup-form.gender-options.female')</option>
                                <option value="other" @selected(old('gender')==='other')>@lang('shop::app.customers.signup-form.gender-options.other')</option>
                            </x-shop::form.control-group.control>
                            <x-shop::form.control-group.error control-name="gender" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.date-of-birth')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="date" name="date_of_birth" :value="old('date_of_birth')" />
                            <x-shop::form.control-group.error control-name="date_of_birth" />
                        </x-shop::form.control-group>
                    </div>

                    <div class="grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.zip-postal-code')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="zip_postal_code" :value="old('zip_postal_code')" />
                            <x-shop::form.control-group.error control-name="zip_postal_code" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.city')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="city" :value="old('city')" />
                            <x-shop::form.control-group.error control-name="city" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.state')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="state" :value="old('state')" />
                            <x-shop::form.control-group.error control-name="state" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>
                                @lang('shop::app.customers.signup-form.country')
                            </x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="country" :value="old('country')" />
                            <x-shop::form.control-group.error control-name="country" />
                        </x-shop::form.control-group>
                    </div>

                    <!-- Wholesale only fields -->
                    <div class="mt-8 grid grid-cols-2 gap-5 max-sm:grid-cols-1">
                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.company')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="company" :value="old('company')" />
                            <x-shop::form.control-group.error control-name="company" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.vat-number')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="vat_number" :value="old('vat_number')" />
                            <x-shop::form.control-group.error control-name="vat_number" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.identification-number')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="identification_number" :value="old('identification_number')" />
                            <x-shop::form.control-group.error control-name="identification_number" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.pec')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="pec" :value="old('pec')" />
                            <x-shop::form.control-group.error control-name="pec" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group class="col-span-2">
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.address')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="address" :value="old('address')" />
                            <x-shop::form.control-group.error control-name="address" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.recipient-code')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="recipient_code" :value="old('recipient_code')" />
                            <x-shop::form.control-group.error control-name="recipient_code" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group>
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.invoice-type')</x-shop::form.control-group.label>
                            <x-shop::form.control-group.control type="text" name="invoice_type" :value="old('invoice_type')" />
                            <x-shop::form.control-group.error control-name="invoice_type" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group class="col-span-2">
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.activity-types')</x-shop::form.control-group.label>
                            <div class="grid grid-cols-1 gap-4 max-sm:grid-cols-1">
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Wholesaler of telephone accessories" class="peer hidden" @checked(in_array('Wholesaler of telephone accessories', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.wholesaler-of-telephone-accessories')</span>
                                </label>

                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Smartphone wholesaler" class="peer hidden" @checked(in_array('Smartphone wholesaler', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.smartphone-wholesaler')</span>
                                </label>

                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Repair Center" class="peer hidden" @checked(in_array('Repair Center', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.repair-center')</span>
                                </label>

                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Bar/Tobacconist" class="peer hidden" @checked(in_array('Bar/Tobacconist', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.bar-tobacconist')</span>
                                </label>

                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Retailer of telephone accessories" class="peer hidden" @checked(in_array('Retailer of telephone accessories', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.retailer-of-telephone-accessories')</span>
                                </label>

                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Smartphone retailer" class="peer hidden" @checked(in_array('Smartphone retailer', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.smartphone-retailer')</span>
                                </label>

                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="activity_types[]" value="Stationery/Copy shop" class="peer hidden" @checked(in_array('Stationery/Copy shop', old('activity_types', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.activity-types-options.stationery-copy-shop')</span>
                                </label>
                            </div>
                            <x-shop::form.control-group.error control-name="activity_types" />
                        </x-shop::form.control-group>

                        <x-shop::form.control-group class="col-span-2">
                            <x-shop::form.control-group.label>@lang('shop::app.customers.signup-form.wholesale.how-know-us')</x-shop::form.control-group.label>
                            <div class="grid grid-cols-3 gap-4 max-sm:grid-cols-1">
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Agent" class="peer hidden" @checked(in_array('Agent', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.agent')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Facebook" class="peer hidden" @checked(in_array('Facebook', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.facebook')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Amazon" class="peer hidden" @checked(in_array('Amazon', old('how_ow_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.amazon')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Web search" class="peer hidden" @checked(in_array('Web search', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.web-search')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Instagram" class="peer hidden" @checked(in_array('Instagram', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.instagram')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="TikTok" class="peer hidden" @checked(in_array('TikTok', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.tiktok')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="LinkedIn" class="peer hidden" @checked(in_array('LinkedIn', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.linkedin')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Pinterest" class="peer hidden" @checked(in_array('Pinterest', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.pinterest')</span>
                                </label>
                                <label class="flex items-center gap-3">
                                    <input type="checkbox" name="how_know_us[]" value="Heard about it" class="peer hidden" @checked(in_array('Heard about it', old('how_know_us', [])))>
                                    <span class="icon-uncheck peer-checked:icon-check-box text-2xl text-navyBlue"></span>
                                    <span>@lang('shop::app.customers.signup-form.wholesale.how-know-us-options.heard-about-it')</span>
                                </label>
                            </div>
                            <x-shop::form.control-group.error control-name="how_know_us" />
                        </x-shop::form.control-group>
                    </div>

                    <!-- Consent & Newsletter -->
                    <div class="mt-4 grid grid-cols-1 gap-4">
                        <!-- Privacy/Marketing Consent -->
                        <x-shop::form.control-group>
                            <label class="flex select-none items-start gap-3">
                                <x-shop::form.control-group.control
                                    type="checkbox"
                                    name="marketing_consent"
                                    id="marketing-consent"
                                    value="1"
                                    rules="required"
                                />

                                <span class="text-base text-zinc-700">
                                    @lang('shop::app.customers.signup-form.marketing-consent')
                                </span>
                            </label>

                            <x-shop::form.control-group.error control-name="marketing_consent" />
                        </x-shop::form.control-group>

                        <!-- Newsletter -->
                        <x-shop::form.control-group>
                            <label class="flex select-none items-start gap-3">
                                <x-shop::form.control-group.control
                                    type="checkbox"
                                    name="is_subscribed"
                                    id="is-subscribed-wholesale"
                                    value="1"
                                />

                                <span>
                                    <span class="block text-base text-zinc-700">
                                        @lang('shop::app.customers.signup-form.subscribe-to-newsletter')
                                    </span>
                                    <span class="block text-sm text-zinc-400">
                                        @lang('shop::app.customers.signup-form.newsletter-hint')
                                    </span>
                                </span>
                            </label>
                        </x-shop::form.control-group>
                    </div>

                    @if (core()->getConfigData('customer.captcha.credentials.status'))
                        <div class="mb-5 flex">
                            {!! \Webkul\Customer\Facades\Captcha::render() !!}
                        </div>
                    @endif

                    <div class="mt-8 flex flex-wrap items-center gap-9 max-sm:justify-center max-sm:gap-5">
                        <button class="primary-button m-0 mx-auto block w-full max-w-[374px] rounded-2xl px-11 py-4 text-center text-base">
                            @lang('shop::app.customers.signup-form.button-title')
                        </button>
                    </div>
                </x-shop::form>
            </div>

            <p class="mt-5 font-medium text-zinc-500 max-sm:text-center max-sm:text-sm">
                @lang('shop::app.customers.signup-form.account-exists')
                <a class="text-navyBlue" href="{{ route('shop.customer.session.index') }}">
                    @lang('shop::app.customers.signup-form.sign-in-button')
                </a>
            </p>
        </div>

        <p class="mb-4 mt-8 text-center text-xs text-zinc-500">
            @lang('shop::app.customers.signup-form.footer', ['current_year'=> date('Y') ])
        </p>
    </div>

    @push('scripts')
        {!! \Webkul\Customer\Facades\Captcha::renderJS() !!}
    @endpush
</x-shop::layouts>


