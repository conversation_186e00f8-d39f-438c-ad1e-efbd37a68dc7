{"__meta": {"id": "01K40CNS3Q4QYVRBH3YJWBNXT8", "datetime": "2025-08-31 16:42:00", "utime": **********.824279, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (12)", "Webkul\\Core\\Models\\Currency (1)", "Webkul\\Core\\Models\\ChannelTranslation (5)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 1.28, "duration_str": "1.28s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.38, "duration_str": "380ms", "connection": "mlk"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (13)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (82)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 and `theme_code` = 'default' order by `sort_order` asc", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (14, 18, 19, 20, 22, 23, 24, 38, 41, 44, 46, 50)", "duration": 0.73, "duration_str": "730ms", "connection": "mlk"}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (24)", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.450982, "end": **********.83207, "duration": 0.3810880184173584, "duration_str": "381ms", "measures": [{"label": "Booting", "start": **********.450982, "relative_start": 0, "end": **********.606141, "relative_end": **********.606141, "duration": 0.*****************, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.606152, "relative_start": 0.*****************, "end": **********.832072, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.615384, "relative_start": 0.*****************, "end": **********.617816, "relative_end": **********.617816, "duration": 0.0024318695068359375, "duration_str": "2.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.672912, "relative_start": 0.*****************, "end": **********.823089, "relative_end": **********.823089, "duration": 0.***************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::home.index", "start": **********.674115, "relative_start": 0.*****************, "end": **********.674115, "relative_end": **********.674115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.carousel.index", "start": **********.695038, "relative_start": 0.*****************, "end": **********.695038, "relative_end": **********.695038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.695503, "relative_start": 0.244520902633667, "end": **********.695503, "relative_end": **********.695503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.706653, "relative_start": 0.25567102432250977, "end": **********.706653, "relative_end": **********.706653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.707135, "relative_start": 0.256152868270874, "end": **********.707135, "relative_end": **********.707135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.707479, "relative_start": 0.2564969062805176, "end": **********.707479, "relative_end": **********.707479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.card", "start": **********.707822, "relative_start": 0.2568399906158447, "end": **********.707822, "relative_end": **********.707822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.714097, "relative_start": 0.26311492919921875, "end": **********.714097, "relative_end": **********.714097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.715952, "relative_start": 0.2649698257446289, "end": **********.715952, "relative_end": **********.715952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.718954, "relative_start": 0.2679719924926758, "end": **********.718954, "relative_end": **********.718954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.72173, "relative_start": 0.2707479000091553, "end": **********.72173, "relative_end": **********.72173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.723238, "relative_start": 0.27225589752197266, "end": **********.723238, "relative_end": **********.723238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.723634, "relative_start": 0.27265191078186035, "end": **********.723634, "relative_end": **********.723634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.723819, "relative_start": 0.27283692359924316, "end": **********.723819, "relative_end": **********.723819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.categories.carousel", "start": **********.726024, "relative_start": 0.2750418186187744, "end": **********.726024, "relative_end": **********.726024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.726486, "relative_start": 0.27550387382507324, "end": **********.726486, "relative_end": **********.726486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.727696, "relative_start": 0.27671384811401367, "end": **********.727696, "relative_end": **********.727696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.727876, "relative_start": 0.27689385414123535, "end": **********.727876, "relative_end": **********.727876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.739636, "relative_start": 0.2886538505554199, "end": **********.739636, "relative_end": **********.739636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.73994, "relative_start": 0.2889578342437744, "end": **********.73994, "relative_end": **********.73994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.740114, "relative_start": 0.28913187980651855, "end": **********.740114, "relative_end": **********.740114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.74208, "relative_start": 0.29109787940979004, "end": **********.74208, "relative_end": **********.74208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.742325, "relative_start": 0.29134297370910645, "end": **********.742325, "relative_end": **********.742325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.742482, "relative_start": 0.2914998531341553, "end": **********.742482, "relative_end": **********.742482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.744423, "relative_start": 0.2934408187866211, "end": **********.744423, "relative_end": **********.744423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.74466, "relative_start": 0.29367780685424805, "end": **********.74466, "relative_end": **********.74466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.744821, "relative_start": 0.2938389778137207, "end": **********.744821, "relative_end": **********.744821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.cms.carousel", "start": **********.748716, "relative_start": 0.29773402214050293, "end": **********.748716, "relative_end": **********.748716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.cms.carousel", "start": **********.749149, "relative_start": 0.29816699028015137, "end": **********.749149, "relative_end": **********.749149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.cms.carousel", "start": **********.749384, "relative_start": 0.2984018325805664, "end": **********.749384, "relative_end": **********.749384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.brand.carousel", "start": **********.751481, "relative_start": 0.30049896240234375, "end": **********.751481, "relative_end": **********.751481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.brand.carousel", "start": **********.751904, "relative_start": 0.3009219169616699, "end": **********.751904, "relative_end": **********.751904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.brand.carousel", "start": **********.752134, "relative_start": 0.30115199089050293, "end": **********.752134, "relative_end": **********.752134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.help.contact-modal", "start": **********.755496, "relative_start": 0.30451393127441406, "end": **********.755496, "relative_end": **********.755496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.758037, "relative_start": 0.3070549964904785, "end": **********.758037, "relative_end": **********.758037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.758493, "relative_start": 0.3075108528137207, "end": **********.758493, "relative_end": **********.758493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.759199, "relative_start": 0.30821681022644043, "end": **********.759199, "relative_end": **********.759199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.759504, "relative_start": 0.30852198600769043, "end": **********.759504, "relative_end": **********.759504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.759751, "relative_start": 0.30876898765563965, "end": **********.759751, "relative_end": **********.759751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.759983, "relative_start": 0.30900096893310547, "end": **********.759983, "relative_end": **********.759983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.76044, "relative_start": 0.30945801734924316, "end": **********.76044, "relative_end": **********.76044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.760654, "relative_start": 0.30967187881469727, "end": **********.760654, "relative_end": **********.760654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.760847, "relative_start": 0.30986499786376953, "end": **********.760847, "relative_end": **********.760847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.761054, "relative_start": 0.3100719451904297, "end": **********.761054, "relative_end": **********.761054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.761436, "relative_start": 0.3104538917541504, "end": **********.761436, "relative_end": **********.761436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.761598, "relative_start": 0.31061601638793945, "end": **********.761598, "relative_end": **********.761598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.761752, "relative_start": 0.31076979637145996, "end": **********.761752, "relative_end": **********.761752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.761926, "relative_start": 0.3109438419342041, "end": **********.761926, "relative_end": **********.761926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.762284, "relative_start": 0.31130194664001465, "end": **********.762284, "relative_end": **********.762284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.76244, "relative_start": 0.31145787239074707, "end": **********.76244, "relative_end": **********.76244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.763513, "relative_start": 0.3125309944152832, "end": **********.763513, "relative_end": **********.763513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.help.floating-icons", "start": **********.764043, "relative_start": 0.313060998916626, "end": **********.764043, "relative_end": **********.764043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.764525, "relative_start": 0.31354284286499023, "end": **********.764525, "relative_end": **********.764525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.768352, "relative_start": 0.3173699378967285, "end": **********.768352, "relative_end": **********.768352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.768735, "relative_start": 0.3177528381347656, "end": **********.768735, "relative_end": **********.768735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.769075, "relative_start": 0.31809282302856445, "end": **********.769075, "relative_end": **********.769075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.769394, "relative_start": 0.31841182708740234, "end": **********.769394, "relative_end": **********.769394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.index", "start": **********.769778, "relative_start": 0.31879591941833496, "end": **********.769778, "relative_end": **********.769778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.top", "start": **********.771263, "relative_start": 0.3202807903289795, "end": **********.771263, "relative_end": **********.771263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.771845, "relative_start": 0.3208630084991455, "end": **********.771845, "relative_end": **********.771845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.777745, "relative_start": 0.3267629146575928, "end": **********.777745, "relative_end": **********.777745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.index", "start": **********.782195, "relative_start": 0.33121299743652344, "end": **********.782195, "relative_end": **********.782195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.bottom", "start": **********.78251, "relative_start": 0.3315279483795166, "end": **********.78251, "relative_end": **********.78251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.787753, "relative_start": 0.33677101135253906, "end": **********.787753, "relative_end": **********.787753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.components.option-badges", "start": **********.791144, "relative_start": 0.3401618003845215, "end": **********.791144, "relative_end": **********.791144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.791542, "relative_start": 0.3405599594116211, "end": **********.791542, "relative_end": **********.791542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.792132, "relative_start": 0.34114980697631836, "end": **********.792132, "relative_end": **********.792132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.794279, "relative_start": 0.34329700469970703, "end": **********.794279, "relative_end": **********.794279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.795327, "relative_start": 0.3443448543548584, "end": **********.795327, "relative_end": **********.795327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.mobile.index", "start": **********.795753, "relative_start": 0.3447709083557129, "end": **********.795753, "relative_end": **********.795753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.798715, "relative_start": 0.3477330207824707, "end": **********.798715, "relative_end": **********.798715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.799191, "relative_start": 0.3482089042663574, "end": **********.799191, "relative_end": **********.799191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.801666, "relative_start": 0.35068392753601074, "end": **********.801666, "relative_end": **********.801666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.802958, "relative_start": 0.3519759178161621, "end": **********.802958, "relative_end": **********.802958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.803213, "relative_start": 0.3522307872772217, "end": **********.803213, "relative_end": **********.803213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.services", "start": **********.805546, "relative_start": 0.35456395149230957, "end": **********.805546, "relative_end": **********.805546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.footer.index", "start": **********.806975, "relative_start": 0.35599279403686523, "end": **********.806975, "relative_end": **********.806975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.8162, "relative_start": 0.365217924118042, "end": **********.8162, "relative_end": **********.8162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.819653, "relative_start": 0.3686709403991699, "end": **********.819653, "relative_end": **********.819653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.820093, "relative_start": 0.3691108226776123, "end": **********.820093, "relative_end": **********.820093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.820287, "relative_start": 0.369304895401001, "end": **********.820287, "relative_end": **********.820287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.820823, "relative_start": 0.3698408603668213, "end": **********.820823, "relative_end": **********.820823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.821189, "relative_start": 0.3702068328857422, "end": **********.821189, "relative_end": **********.821189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45599168, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 83, "nb_templates": 83, "templates": [{"name": "1x shop::home.index", "param_count": null, "params": [], "start": **********.6741, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::home.index"}, {"name": "1x shop::components.carousel.index", "param_count": null, "params": [], "start": **********.695026, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/carousel/index.blade.phpshop::components.carousel.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcarousel%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.carousel.index"}, {"name": "4x shop::components.media.images.lazy", "param_count": null, "params": [], "start": **********.695492, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/media/images/lazy.blade.phpshop::components.media.images.lazy", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages%2Flazy.blade.php&line=1", "ajax": false, "filename": "lazy.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.media.images.lazy"}, {"name": "4x shop::components.products.carousel", "param_count": null, "params": [], "start": **********.70664, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/carousel.blade.phpshop::components.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.products.carousel"}, {"name": "5x shop::components.shimmer.products.carousel", "param_count": null, "params": [], "start": **********.707124, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/carousel.blade.phpshop::components.shimmer.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.carousel"}, {"name": "5x shop::components.shimmer.products.cards.grid", "param_count": null, "params": [], "start": **********.707469, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/cards/grid.blade.phpshop::components.shimmer.products.cards.grid", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcards%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.cards.grid"}, {"name": "1x shop::components.products.card", "param_count": null, "params": [], "start": **********.707812, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/card.blade.phpshop::components.products.card", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.products.card"}, {"name": "2x shop::components.products.ratings", "param_count": null, "params": [], "start": **********.715941, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/ratings.blade.phpshop::components.products.ratings", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.ratings"}, {"name": "1x shop::components.button.index", "param_count": null, "params": [], "start": **********.723226, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/button/index.blade.phpshop::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.button.index"}, {"name": "1x shop::components.categories.carousel", "param_count": null, "params": [], "start": **********.726014, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/categories/carousel.blade.phpshop::components.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.categories.carousel"}, {"name": "2x shop::components.shimmer.categories.carousel", "param_count": null, "params": [], "start": **********.726475, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/categories/carousel.blade.phpshop::components.shimmer.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.shimmer.categories.carousel"}, {"name": "1x shop::components.cms.carousel", "param_count": null, "params": [], "start": **********.748706, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/cms/carousel.blade.phpshop::components.cms.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcms%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.cms.carousel"}, {"name": "2x shop::components.shimmer.cms.carousel", "param_count": null, "params": [], "start": **********.749139, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/cms/carousel.blade.phpshop::components.shimmer.cms.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fcms%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.shimmer.cms.carousel"}, {"name": "1x shop::components.brand.carousel", "param_count": null, "params": [], "start": **********.75147, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/brand/carousel.blade.phpshop::components.brand.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbrand%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.brand.carousel"}, {"name": "2x shop::components.shimmer.brand.carousel", "param_count": null, "params": [], "start": **********.751894, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/brand/carousel.blade.phpshop::components.shimmer.brand.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fbrand%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.shimmer.brand.carousel"}, {"name": "1x shop::components.help.contact-modal", "param_count": null, "params": [], "start": **********.755483, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/help/contact-modal.blade.phpshop::components.help.contact-modal", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fhelp%2Fcontact-modal.blade.php&line=1", "ajax": false, "filename": "contact-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.help.contact-modal"}, {"name": "4x shop::components.form.control-group.label", "param_count": null, "params": [], "start": **********.758024, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/label.blade.phpshop::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.form.control-group.label"}, {"name": "5x shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.75848, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.form.control-group.control"}, {"name": "5x shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.759187, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.form.control-group.error"}, {"name": "4x shop::components.form.control-group.index", "param_count": null, "params": [], "start": **********.759492, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/index.blade.phpshop::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.form.control-group.index"}, {"name": "2x shop::components.form.index", "param_count": null, "params": [], "start": **********.763502, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.form.index"}, {"name": "1x shop::components.help.floating-icons", "param_count": null, "params": [], "start": **********.764034, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/help/floating-icons.blade.phpshop::components.help.floating-icons", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fhelp%2Ffloating-icons.blade.php&line=1", "ajax": false, "filename": "floating-icons.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.help.floating-icons"}, {"name": "1x shop::components.layouts.index", "param_count": null, "params": [], "start": **********.764514, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.index"}, {"name": "1x shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.768341, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.flash-group.index"}, {"name": "2x shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.768718, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.flash-group.item"}, {"name": "1x shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.769383, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.confirm"}, {"name": "1x shop::components.layouts.header.index", "param_count": null, "params": [], "start": **********.769768, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.phpshop::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.index"}, {"name": "1x shop::components.layouts.header.desktop.top", "param_count": null, "params": [], "start": **********.771252, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.phpshop::components.layouts.header.desktop.top", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.top"}, {"name": "4x shop::components.dropdown.index", "param_count": null, "params": [], "start": **********.771832, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/dropdown/index.blade.phpshop::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.dropdown.index"}, {"name": "1x shop::components.layouts.header.desktop.index", "param_count": null, "params": [], "start": **********.782184, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/index.blade.phpshop::components.layouts.header.desktop.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.index"}, {"name": "1x shop::components.layouts.header.desktop.bottom", "param_count": null, "params": [], "start": **********.7825, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.phpshop::components.layouts.header.desktop.bottom", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=1", "ajax": false, "filename": "bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.bottom"}, {"name": "2x shop::checkout.cart.mini-cart", "param_count": null, "params": [], "start": **********.787742, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/checkout/cart/mini-cart.blade.phpshop::checkout.cart.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcart%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::checkout.cart.mini-cart"}, {"name": "1x shop::checkout.components.option-badges", "param_count": null, "params": [], "start": **********.791133, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/checkout/components/option-badges.blade.phpshop::checkout.components.option-badges", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcomponents%2Foption-badges.blade.php&line=1", "ajax": false, "filename": "option-badges.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::checkout.components.option-badges"}, {"name": "1x shop::components.quantity-changer.index", "param_count": null, "params": [], "start": **********.791532, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/quantity-changer/index.blade.phpshop::components.quantity-changer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fquantity-changer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.quantity-changer.index"}, {"name": "5x shop::components.drawer.index", "param_count": null, "params": [], "start": **********.792122, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/drawer/index.blade.phpshop::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.drawer.index"}, {"name": "1x shop::components.layouts.header.mobile.index", "param_count": null, "params": [], "start": **********.795743, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.phpshop::components.layouts.header.mobile.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.mobile.index"}, {"name": "1x shop::components.layouts.services", "param_count": null, "params": [], "start": **********.805535, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.phpshop::components.layouts.services", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fservices.blade.php&line=1", "ajax": false, "filename": "services.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.services"}, {"name": "1x shop::components.layouts.footer.index", "param_count": null, "params": [], "start": **********.806964, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.phpshop::components.layouts.footer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ffooter%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.footer.index"}, {"name": "1x shop::components.accordion.index", "param_count": null, "params": [], "start": **********.81619, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/accordion/index.blade.phpshop::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.accordion.index"}, {"name": "1x core::blade.tracer.style", "param_count": null, "params": [], "start": **********.820813, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "core::blade.tracer.style"}, {"name": "1x paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.821179, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "paypal::checkout.onepage.paypal-smart-button"}]}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.012159999999999997, "accumulated_duration_str": "12.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.631001, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 10.526}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.635422, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 10.526, "width_percent": 3.125}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.637414, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 13.651, "width_percent": 2.467}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.6430302, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 16.118, "width_percent": 2.22}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.643985, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 18.339, "width_percent": 2.385}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.6448622, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 20.724, "width_percent": 2.22}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.64555, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 22.944, "width_percent": 1.398}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"facc3804-74b2-495b-8fdc-8ba6544c1179\\\",\\\"displayName\\\":\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\",\\\"command\\\":\\\"O:39:\\\\\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\\\\\":1:{s:6:\\\\\\\"\\\\u0000*\\\\u0000log\\\\\\\";a:14:{s:6:\\\\\\\"method\\\\\\\";s:3:\\\\\\\"GET\\\\\\\";s:7:\\\\\\\"request\\\\\\\";a:0:{}s:3:\\\\\\\"url\\\\\\\";s:15:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\\\\";s:7:\\\\\\\"referer\\\\\\\";s:37:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/admin\\\\/settings\\\\/themes\\\\\\\";s:9:\\\\\\\"languages\\\\\\\";a:0:{}s:9:\\\\\\\"useragent\\\\\\\";s:111:\\\\\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\\\\\";s:7:\\\\\\\"headers\\\\\\\";a:11:{s:6:\\\\\\\"cookie\\\\\\\";a:1:{i:0;s:749:\\\\\\\"cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IlVDbVZnekI1OVNOV2N2eUlkRS9kdWc9PSIsInZhbHVlIjoiTk1ERTBma2l4S3gxYXVWeThCdUVXMlRZakpTVVpMRHNwZDJ6b1hIZWdXWEpxQVd0N1lHNFFOOUh2STBscE0vRjVNN3VEZlBFNmp0VEZBeThHeFJadE1pbm1mV0ZCTXZMZ3c2Z3RNOUgwUUdMcWNXVUxpMFpJTG9ReU14Slk4R2kiLCJtYWMiOiI1MTgwOGMyNzFjYjBlMWU0NzBiNGIxM2U4MzdjMTM1YjRkNWQ0YWM0NTNkMThmMzU4MWNhNDI3ZTBiNmI1YmY0IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IllTTkI0UG9wQ3FFTlV0WDhWcWtKR1E9PSIsInZhbHVlIjoiZlptbjhZQWpKOWFxSWZxclJ3OWtXL2NGQk9SUzkzM1pRUVdPTFJQelQ2Vmpsc1pJNG9iWHh1cm9Ob1VkRmtUVGxER3BodjhpRDAveEFHS09ZV2haemxGTnRBUXp4RytCMGlWVHFTTldIQ0xxdFoyT2l5eVdhV29XLy91WWZHekEiLCJtYWMiOiJmY2NmMmU4MTc2OTk4MzIzZTRhZGY4M2MxZDhkYmIyYWRhOTY4MDA2MjlkZGU5OTk0ZDRiYWNmZmI1NDIwZTk0IiwidGFnIjoiIn0%3D\\\\\\\";}s:15:\\\\\\\"accept-language\\\\\\\";a:1:{i:0;s:23:\\\\\\\"zh-CN,zh;q=0.9,en;q=0.8\\\\\\\";}s:15:\\\\\\\"accept-encoding\\\\\\\";a:1:{i:0;s:13:\\\\\\\"gzip, deflate\\\\\\\";}s:7:\\\\\\\"referer\\\\\\\";a:1:{i:0;s:37:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/admin\\\\/settings\\\\/themes\\\\\\\";}s:6:\\\\\\\"accept\\\\\\\";a:1:{i:0;s:135:\\\\\\\"text\\\\/html,application\\\\/xhtml+xml,application\\\\/xml;q=0.9,image\\\\/avif,image\\\\/webp,image\\\\/apng,*\\\\/*;q=0.8,application\\\\/signed-exchange;v=b3;q=0.7\\\\\\\";}s:10:\\\\\\\"user-agent\\\\\\\";a:1:{i:0;s:111:\\\\\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\\\\\";}s:25:\\\\\\\"upgrade-insecure-requests\\\\\\\";a:1:{i:0;s:1:\\\\\\\"1\\\\\\\";}s:13:\\\\\\\"cache-control\\\\\\\";a:1:{i:0;s:8:\\\\\\\"no-cache\\\\\\\";}s:6:\\\\\\\"pragma\\\\\\\";a:1:{i:0;s:8:\\\\\\\"no-cache\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";a:1:{i:0;s:10:\\\\\\\"keep-alive\\\\\\\";}s:4:\\\\\\\"host\\\\\\\";a:1:{i:0;s:8:\\\\\\\"mlk.test\\\\\\\";}}s:6:\\\\\\\"device\\\\\\\";s:0:\\\\\\\"\\\\\\\";s:8:\\\\\\\"platform\\\\\\\";s:7:\\\\\\\"Windows\\\\\\\";s:7:\\\\\\\"browser\\\\\\\";s:6:\\\\\\\"Chrome\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";s:9:\\\\\\\"127.0.0.1\\\\\\\";s:10:\\\\\\\"visitor_id\\\\\\\";N;s:12:\\\\\\\"visitor_type\\\\\\\";N;s:10:\\\\\\\"channel_id\\\\\\\";i:1;}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"facc3804-74b2-495b-8fdc-8ba6544c1179\",\"displayName\":\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\",\"job\":\"Illuminate\\\\Queue\\\\CallQ<PERSON>ued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\",\"command\":\"O:39:\\\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\\\":1:{s:6:\\\"\\u0000*\\u0000log\\\";a:14:{s:6:\\\"method\\\";s:3:\\\"GET\\\";s:7:\\\"request\\\";a:0:{}s:3:\\\"url\\\";s:15:\\\"http:\\/\\/mlk.test\\\";s:7:\\\"referer\\\";s:37:\\\"http:\\/\\/mlk.test\\/admin\\/settings\\/themes\\\";s:9:\\\"languages\\\";a:0:{}s:9:\\\"useragent\\\";s:111:\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\";s:7:\\\"headers\\\";a:11:{s:6:\\\"cookie\\\";a:1:{i:0;s:749:\\\"cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IlVDbVZnekI1OVNOV2N2eUlkRS9kdWc9PSIsInZhbHVlIjoiTk1ERTBma2l4S3gxYXVWeThCdUVXMlRZakpTVVpMRHNwZDJ6b1hIZWdXWEpxQVd0N1lHNFFOOUh2STBscE0vRjVNN3VEZlBFNmp0VEZBeThHeFJadE1pbm1mV0ZCTXZMZ3c2Z3RNOUgwUUdMcWNXVUxpMFpJTG9ReU14Slk4R2kiLCJtYWMiOiI1MTgwOGMyNzFjYjBlMWU0NzBiNGIxM2U4MzdjMTM1YjRkNWQ0YWM0NTNkMThmMzU4MWNhNDI3ZTBiNmI1YmY0IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IllTTkI0UG9wQ3FFTlV0WDhWcWtKR1E9PSIsInZhbHVlIjoiZlptbjhZQWpKOWFxSWZxclJ3OWtXL2NGQk9SUzkzM1pRUVdPTFJQelQ2Vmpsc1pJNG9iWHh1cm9Ob1VkRmtUVGxER3BodjhpRDAveEFHS09ZV2haemxGTnRBUXp4RytCMGlWVHFTTldIQ0xxdFoyT2l5eVdhV29XLy91WWZHekEiLCJtYWMiOiJmY2NmMmU4MTc2OTk4MzIzZTRhZGY4M2MxZDhkYmIyYWRhOTY4MDA2MjlkZGU5OTk0ZDRiYWNmZmI1NDIwZTk0IiwidGFnIjoiIn0%3D\\\";}s:15:\\\"accept-language\\\";a:1:{i:0;s:23:\\\"zh-CN,zh;q=0.9,en;q=0.8\\\";}s:15:\\\"accept-encoding\\\";a:1:{i:0;s:13:\\\"gzip, deflate\\\";}s:7:\\\"referer\\\";a:1:{i:0;s:37:\\\"http:\\/\\/mlk.test\\/admin\\/settings\\/themes\\\";}s:6:\\\"accept\\\";a:1:{i:0;s:135:\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\";}s:10:\\\"user-agent\\\";a:1:{i:0;s:111:\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\";}s:25:\\\"upgrade-insecure-requests\\\";a:1:{i:0;s:1:\\\"1\\\";}s:13:\\\"cache-control\\\";a:1:{i:0;s:8:\\\"no-cache\\\";}s:6:\\\"pragma\\\";a:1:{i:0;s:8:\\\"no-cache\\\";}s:10:\\\"connection\\\";a:1:{i:0;s:10:\\\"keep-alive\\\";}s:4:\\\"host\\\";a:1:{i:0;s:8:\\\"mlk.test\\\";}}s:6:\\\"device\\\";s:0:\\\"\\\";s:8:\\\"platform\\\";s:7:\\\"Windows\\\";s:7:\\\"browser\\\";s:6:\\\"Chrome\\\";s:2:\\\"ip\\\";s:9:\\\"127.0.0.1\\\";s:10:\\\"visitor_id\\\";N;s:12:\\\"visitor_type\\\";N;s:10:\\\"channel_id\\\";i:1;}}\"}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 99}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 338}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 244}], "start": **********.663244, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:188", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=188", "ajax": false, "filename": "DatabaseQueue.php", "line": "188"}, "connection": "mlk", "explain": null, "start_percent": 24.342, "width_percent": 32.237}, {"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 and `theme_code` = 'default' order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 1, "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/HomeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\HomeController.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.6681812, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 56.579, "width_percent": 2.303}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (14, 18, 19, 20, 22, 23, 24, 38, 41, 44, 46, 50)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/HomeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\HomeController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.670029, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 58.882, "width_percent": 6.003}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "shop::home.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "line": 9}], "start": **********.681531, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 64.885, "width_percent": 15.954}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.770267, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mlk", "explain": null, "start_percent": 80.839, "width_percent": 2.961}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 148}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.777155, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:148", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=148", "ajax": false, "filename": "top.blade.php", "line": "148"}, "connection": "mlk", "explain": null, "start_percent": 83.799, "width_percent": 1.563}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 275}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.780988, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:275", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=275", "ajax": false, "filename": "top.blade.php", "line": "275"}, "connection": "mlk", "explain": null, "start_percent": 85.362, "width_percent": 1.974}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.800718, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mlk", "explain": null, "start_percent": 87.336, "width_percent": 3.783}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8021781, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:442", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=442", "ajax": false, "filename": "index.blade.php", "line": "442"}, "connection": "mlk", "explain": null, "start_percent": 91.118, "width_percent": 2.549}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["services_content", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.services", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.805964, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 93.668, "width_percent": 2.303}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["footer_links", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.8075259, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 95.97, "width_percent": 1.891}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.808359, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 97.862, "width_percent": 2.138}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 82, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 114, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test", "action_name": "shop.home.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\HomeController@index", "uri": "GET /", "controller": "Webkul\\Shop\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FHomeController.php&line=29\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FHomeController.php&line=29\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/HomeController.php:29-55</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, cache.response", "duration": "383ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-173345473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-173345473\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1073495537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1073495537\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-854950811 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"749 characters\">cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IlVDbVZnekI1OVNOV2N2eUlkRS9kdWc9PSIsInZhbHVlIjoiTk1ERTBma2l4S3gxYXVWeThCdUVXMlRZakpTVVpMRHNwZDJ6b1hIZWdXWEpxQVd0N1lHNFFOOUh2STBscE0vRjVNN3VEZlBFNmp0VEZBeThHeFJadE1pbm1mV0ZCTXZMZ3c2Z3RNOUgwUUdMcWNXVUxpMFpJTG9ReU14Slk4R2kiLCJtYWMiOiI1MTgwOGMyNzFjYjBlMWU0NzBiNGIxM2U4MzdjMTM1YjRkNWQ0YWM0NTNkMThmMzU4MWNhNDI3ZTBiNmI1YmY0IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IllTTkI0UG9wQ3FFTlV0WDhWcWtKR1E9PSIsInZhbHVlIjoiZlptbjhZQWpKOWFxSWZxclJ3OWtXL2NGQk9SUzkzM1pRUVdPTFJQelQ2Vmpsc1pJNG9iWHh1cm9Ob1VkRmtUVGxER3BodjhpRDAveEFHS09ZV2haemxGTnRBUXp4RytCMGlWVHFTTldIQ0xxdFoyT2l5eVdhV29XLy91WWZHekEiLCJtYWMiOiJmY2NmMmU4MTc2OTk4MzIzZTRhZGY4M2MxZDhkYmIyYWRhOTY4MDA2MjlkZGU5OTk0ZDRiYWNmZmI1NDIwZTk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854950811\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1580027407 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie-consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ip_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xav7TvBY50f3PmCrgGGE5IcBX7wYLmN4nP2AjRNz</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fr0YQS4iboJ57u5lCo2muDExMYjaTYIbYbXvHECi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580027407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1053247743 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 15:42:00 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053247743\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1569201164 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xav7TvBY50f3PmCrgGGE5IcBX7wYLmN4nP2AjRNz</span>\"\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>cart</span>\" => {<a class=sf-dump-ref>#1625</a><samp data-depth=2 class=sf-dump-compact>\n    +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": <span class=sf-dump-num>42</span>\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569201164\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test", "action_name": "shop.home.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\HomeController@index"}, "badge": null}}